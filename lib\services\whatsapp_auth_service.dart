import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';

/// خدمة المصادقة عبر الواتساب
class WhatsAppAuthService {
  static const String _baseUrl = 'http://197.63.231.187:3000/api/auth';
  
  /// إرسال رمز التحقق عبر الواتساب
  static Future<Map<String, dynamic>> sendVerificationCode({
    required String phoneNumber,
    String channel = 'whatsapp',
  }) async {
    try {
      // تنسيق رقم الهاتف
      String formattedPhoneNumber = _formatPhoneNumber(phoneNumber);
      
      final response = await http.post(
        Uri.parse('$_baseUrl/send-verification'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'phoneNumber': formattedPhoneNumber,
          'channel': channel,
        }),
      ).timeout(const Duration(seconds: 15));

      final data = jsonDecode(response.body);
      
      if (response.statusCode == 200 && data['success'] == true) {
        return {
          'success': true,
          'message': data['message'] ?? 'تم إرسال رمز التحقق بنجاح',
          'phoneNumber': formattedPhoneNumber,
          'expiryTime': data['data']?['expiryTime'],
          'channel': data['data']?['channel'] ?? channel,
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'فشل إرسال رمز التحقق',
          'error': data['error'],
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('WhatsApp verification error: $e');
      }
      return {
        'success': false,
        'message': 'حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.',
        'error': e.toString(),
      };
    }
  }

  /// التحقق من رمز التحقق
  static Future<Map<String, dynamic>> verifyCode({
    required String phoneNumber,
    required String code,
  }) async {
    try {
      String formattedPhoneNumber = _formatPhoneNumber(phoneNumber);
      
      final response = await http.post(
        Uri.parse('$_baseUrl/verify-code'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'phoneNumber': formattedPhoneNumber,
          'code': code,
        }),
      ).timeout(const Duration(seconds: 10));

      final data = jsonDecode(response.body);
      
      if (response.statusCode == 200 && data['success'] == true) {
        return {
          'success': true,
          'message': data['message'] ?? 'تم التحقق من الرمز بنجاح',
          'phoneNumber': formattedPhoneNumber,
          'verified': true,
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'رمز التحقق غير صحيح',
          'error': data['error'],
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('Code verification error: $e');
      }
      return {
        'success': false,
        'message': 'حدث خطأ في التحقق. يرجى المحاولة مرة أخرى.',
        'error': e.toString(),
      };
    }
  }

  /// إرسال رمز استرداد كلمة المرور
  static Future<Map<String, dynamic>> sendPasswordResetCode({
    required String phoneNumber,
  }) async {
    try {
      String formattedPhoneNumber = _formatPhoneNumber(phoneNumber);
      
      final response = await http.post(
        Uri.parse('$_baseUrl/forgot-password'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'phoneNumber': formattedPhoneNumber,
        }),
      ).timeout(const Duration(seconds: 15));

      final data = jsonDecode(response.body);
      
      if (response.statusCode == 200 && data['success'] == true) {
        return {
          'success': true,
          'message': data['message'] ?? 'تم إرسال رمز الاسترداد بنجاح',
          'phoneNumber': formattedPhoneNumber,
          'expiryTime': data['data']?['expiryTime'],
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'فشل إرسال رمز الاسترداد',
          'error': data['error'],
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('Password reset error: $e');
      }
      return {
        'success': false,
        'message': 'حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.',
        'error': e.toString(),
      };
    }
  }

  /// إعادة تعيين كلمة المرور
  static Future<Map<String, dynamic>> resetPassword({
    required String phoneNumber,
    required String code,
    required String newPassword,
  }) async {
    try {
      String formattedPhoneNumber = _formatPhoneNumber(phoneNumber);
      
      final response = await http.post(
        Uri.parse('$_baseUrl/reset-password'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'phoneNumber': formattedPhoneNumber,
          'code': code,
          'newPassword': newPassword,
        }),
      ).timeout(const Duration(seconds: 10));

      final data = jsonDecode(response.body);
      
      if (response.statusCode == 200 && data['success'] == true) {
        return {
          'success': true,
          'message': data['message'] ?? 'تم تغيير كلمة المرور بنجاح',
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'فشل تغيير كلمة المرور',
          'error': data['error'],
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('Password reset error: $e');
      }
      return {
        'success': false,
        'message': 'حدث خطأ في إعادة تعيين كلمة المرور.',
        'error': e.toString(),
      };
    }
  }

  /// تنسيق رقم الهاتف
  static String _formatPhoneNumber(String phoneNumber) {
    // إزالة جميع الرموز غير الرقمية
    String cleaned = phoneNumber.replaceAll(RegExp(r'\D'), '');
    
    // إضافة رمز مصر إذا لم يكن موجوداً
    if (cleaned.length == 11 && cleaned.startsWith('01')) {
      cleaned = '2$cleaned';
    } else if (cleaned.length == 10 && !cleaned.startsWith('2')) {
      cleaned = '20$cleaned';
    }
    
    // إضافة علامة + إذا لم تكن موجودة
    if (!cleaned.startsWith('+')) {
      cleaned = '+$cleaned';
    }
    
    return cleaned;
  }

  /// التحقق من صحة رقم الهاتف المصري
  static bool isValidEgyptianPhoneNumber(String phoneNumber) {
    String cleaned = phoneNumber.replaceAll(RegExp(r'\D'), '');
    
    // التحقق من الأنماط المختلفة لأرقام الهاتف المصرية
    if (cleaned.length == 11 && cleaned.startsWith('01')) {
      return true; // 01xxxxxxxxx
    } else if (cleaned.length == 12 && cleaned.startsWith('201')) {
      return true; // 201xxxxxxxxx
    } else if (cleaned.length == 13 && cleaned.startsWith('+201')) {
      return true; // +201xxxxxxxxx
    }
    
    return false;
  }

  /// الحصول على رقم الهاتف المنسق للعرض
  static String getDisplayPhoneNumber(String phoneNumber) {
    String cleaned = _formatPhoneNumber(phoneNumber);
    
    // إزالة رمز الدولة للعرض
    if (cleaned.startsWith('+20')) {
      cleaned = cleaned.substring(3);
      return '0$cleaned';
    }
    
    return phoneNumber;
  }

  /// التحقق من حالة الاتصال بالإنترنت
  static Future<bool> checkInternetConnection() async {
    try {
      final response = await http.get(
        Uri.parse('https://www.google.com'),
      ).timeout(const Duration(seconds: 5));
      
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}
