# نظام المصادقة المحسن - Enhanced Authentication System

## 🔐 نظرة عامة

تم تطوير نظام مصادقة محسن يعتمد على رقم الهاتف مع إرسال رسائل التحقق عبر الواتساب، ونظام استرداد كلمة المرور، وخاصية "تذكرني" الآمنة.

## ✨ المميزات الجديدة

### 1. المصادقة عبر الواتساب
- إرسال رموز التحقق عبر WhatsApp Business API
- تنسيق تلقائي لأرقام الهاتف المصرية
- رسائل واضحة ومفهومة باللغة العربية
- نظام انتهاء صلاحية للرموز (10 دقائق)
- حد أقصى للمحاولات (3 محاولات)

### 2. نظام استرداد كلمة المرور
- إرسال رمز استرداد عبر الواتساب
- التحقق من وجود المستخدم قبل الإرسال
- صلاحية ممتدة لرموز الاسترداد (15 دقيقة)
- تشفير آمن لرموز الاسترداد

### 3. خاصية "تذكرني"
- تشفير آمن للبيانات المحفوظة محلياً
- انتهاء صلاحية تلقائي (30 يوم)
- تسجيل دخول تلقائي عند بدء التطبيق
- مسح البيانات عند تسجيل الخروج

### 4. واجهة مستخدم محسنة
- تصميم متدرج جذاب
- رسوم متحركة سلسة
- خطوات واضحة للمصادقة
- رسائل خطأ مفيدة

## 🏗️ البنية التقنية

### الملفات الجديدة

#### Backend
- `backend/src/controllers/verificationController.js` - محدث لدعم الواتساب
- `backend/src/routes/authroutes.js` - محدث بمسارات جديدة

#### Frontend
- `lib/services/secure_storage_service.dart` - خدمة التخزين الآمن
- `lib/services/whatsapp_auth_service.dart` - خدمة المصادقة عبر الواتساب
- `lib/screens/enhanced_login_page.dart` - صفحة تسجيل الدخول المحسنة
- `lib/screens/enhanced_registration_page.dart` - صفحة التسجيل التفصيلية المحسنة

### التحديثات على الملفات الموجودة
- `lib/appstate.dart` - إضافة دعم "تذكرني" والتحقق التلقائي
- `lib/main.dart` - تحديث للصفحة الجديدة

## 🔧 الإعداد والتكوين

### متطلبات Backend

1. **متغيرات البيئة المطلوبة:**
```env
WHATSAPP_API_URL=https://graph.facebook.com/v18.0
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
```

2. **تثبيت المكتبات:**
```bash
npm install axios crypto
```

### متطلبات Frontend

1. **إضافة المكتبات في pubspec.yaml:**
```yaml
dependencies:
  crypto: ^3.0.3
  animate_do: ^3.1.2
```

2. **تثبيت المكتبات:**
```bash
flutter pub get
```

## 📱 كيفية الاستخدام

### 1. تسجيل الدخول العادي
1. إدخال رقم الهاتف وكلمة المرور
2. اختيار "تذكرني" (اختياري)
3. الضغط على "تسجيل الدخول"

### 2. إنشاء حساب جديد عبر الواتساب
1. الضغط على "إنشاء حساب عبر الواتساب" من صفحة تسجيل الدخول
2. إدخال رقم الهاتف والتحقق منه عبر الواتساب
3. ملء البيانات التفصيلية:
   - اسم المستخدم
   - نوع المستخدم (حكيم، عالم، مجتهد، طبي، عضو)
   - كلمة المرور وتأكيدها
   - المهنة (حسب نوع المستخدم)
   - رفع صورة البطاقة الشخصية (للأنواع غير الزائر)
4. إكمال عملية التسجيل

### 3. التسجيل التفصيلي المباشر
1. الضغط على "ليس لديك حساب؟ إنشاء حساب جديد"
2. ملء جميع البيانات المطلوبة
3. التحقق من رقم الهاتف عبر الواتساب
4. إكمال التسجيل

### 4. استرداد كلمة المرور
1. الضغط على "نسيت كلمة المرور؟"
2. إدخال رقم الهاتف
3. إدخال رمز الاسترداد المرسل عبر الواتساب
4. إدخال كلمة المرور الجديدة

## 🔒 الأمان

### تشفير البيانات
- استخدام SHA-256 للتحقق من سلامة البيانات
- تشفير Base64 للبيانات المحفوظة
- مفاتيح تشفير منفصلة لكل نوع بيانات

### إدارة الجلسات
- انتهاء صلاحية تلقائي للرموز
- حد أقصى للمحاولات
- مسح البيانات عند تسجيل الخروج

### التحقق من صحة البيانات
- تنسيق أرقام الهاتف المصرية
- التحقق من قوة كلمة المرور
- التحقق من صحة رموز التحقق

## 🚀 API Endpoints الجديدة

### إرسال رمز التحقق
```
POST /api/auth/send-verification
Body: {
  "phoneNumber": "+201234567890",
  "channel": "whatsapp"
}
```

### التحقق من الرمز
```
POST /api/auth/verify-code
Body: {
  "phoneNumber": "+201234567890",
  "code": "123456"
}
```

### إرسال رمز استرداد كلمة المرور
```
POST /api/auth/forgot-password
Body: {
  "phoneNumber": "+201234567890"
}
```

### إعادة تعيين كلمة المرور
```
POST /api/auth/reset-password
Body: {
  "phoneNumber": "+201234567890",
  "code": "123456",
  "newPassword": "newpassword123"
}
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة

1. **فشل إرسال رسائل الواتساب:**
   - تحقق من صحة WHATSAPP_ACCESS_TOKEN
   - تأكد من تفعيل WhatsApp Business API
   - تحقق من صحة WHATSAPP_PHONE_NUMBER_ID

2. **مشاكل في التشفير:**
   - تأكد من تثبيت مكتبة crypto
   - تحقق من صحة مفاتيح التشفير

3. **مشاكل في حفظ البيانات:**
   - تحقق من أذونات التطبيق
   - تأكد من تثبيت shared_preferences

## 📈 التحسينات المستقبلية

1. **دعم مقدمي خدمة إضافيين:**
   - Telegram
   - SMS عبر مقدمين آخرين

2. **تحسينات الأمان:**
   - 2FA (المصادقة الثنائية)
   - Biometric authentication

3. **تحسينات UX:**
   - Dark mode support
   - المزيد من اللغات

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

---

**تاريخ آخر تحديث:** 2025-01-05
**الإصدار:** 1.0.0
