# ========================================
# إعدادات شهادة التوقيع لتطبيق فلك
# ========================================
# 
# هذا ملف مثال لإعدادات شهادة التوقيق
# انسخ هذا الملف إلى android/key.properties
# وقم بتعديل القيم حسب شهادتك
#
# ملاحظة: لا تشارك هذا الملف في نظام التحكم بالإصدارات
# ========================================

# مسار ملف keystore
storeFile=../release-key.keystore

# كلمة مرور keystore
storePassword=YOUR_KEYSTORE_PASSWORD

# اسم المفتاح (alias)
keyAlias=fulk-release-key

# كلمة مرور المفتاح
keyPassword=YOUR_KEY_PASSWORD

# ========================================
# خطوات إنشاء شهادة التوقيع:
# ========================================
#
# 1. إنشاء keystore جديد:
# keytool -genkey -v -keystore release-key.keystore -alias fulk-release-key -keyalg RSA -keysize 2048 -validity 10000
#
# 2. املأ المعلومات المطلوبة:
# - الاسم الأول والأخير: [اسم الشركة أو المطور]
# - اسم الوحدة التنظيمية: [قسم التطوير]
# - اسم المؤسسة: [اسم الشركة]
# - اسم المدينة: [المدينة]
# - اسم الولاية: [المحافظة]
# - رمز البلد: EG
#
# 3. احفظ الملف في مجلد android/
#
# 4. انسخ هذا الملف إلى android/key.properties
#
# 5. عدّل القيم في key.properties
#
# ========================================
# معلومات مهمة:
# ========================================
#
# - احتفظ بنسخة آمنة من keystore وكلمات المرور
# - فقدان keystore يعني عدم القدرة على تحديث التطبيق
# - استخدم كلمات مرور قوية
# - لا تشارك هذه المعلومات مع أحد
#
# ========================================
