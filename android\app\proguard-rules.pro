# ========================================
# قواعد ProGuard لتطبيق فلك
# ========================================

# إعدادات عامة
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose

# تحسينات الأداء
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification

# الحفاظ على معلومات التصحيح
-keepattributes *Annotation*
-keepattributes Signature
-keepattributes SourceFile,LineNumberTable
-keepattributes EnclosingMethod
-keepattributes InnerClasses

# Flutter
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }

# Firebase
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.firebase.**
-dontwarn com.google.android.gms.**

# Gson
-keepattributes Signature
-keepattributes *Annotation*
-dontwarn sun.misc.**
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# OkHttp
-dontwarn okhttp3.**
-dontwarn okio.**
-dontwarn javax.annotation.**
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# Retrofit
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }
-keepattributes Signature
-keepattributes Exceptions

# Socket.IO
-keep class io.socket.** { *; }
-dontwarn io.socket.**

# WebRTC
-keep class org.webrtc.** { *; }
-dontwarn org.webrtc.**

# Camera
-keep class androidx.camera.** { *; }
-dontwarn androidx.camera.**

# Location
-keep class com.google.android.gms.location.** { *; }
-dontwarn com.google.android.gms.location.**

# Maps
-keep class com.google.android.gms.maps.** { *; }
-dontwarn com.google.android.gms.maps.**

# Permissions
-keep class androidx.core.app.ActivityCompat { *; }
-keep class androidx.core.content.ContextCompat { *; }

# SQLite
-keep class androidx.sqlite.** { *; }
-dontwarn androidx.sqlite.**

# Shared Preferences
-keep class androidx.preference.** { *; }
-dontwarn androidx.preference.**

# Notifications
-keep class androidx.core.app.NotificationCompat** { *; }
-keep class androidx.core.app.NotificationManagerCompat { *; }

# WorkManager
-keep class androidx.work.** { *; }
-dontwarn androidx.work.**

# Lifecycle
-keep class androidx.lifecycle.** { *; }
-dontwarn androidx.lifecycle.**

# Navigation
-keep class androidx.navigation.** { *; }
-dontwarn androidx.navigation.**

# ViewBinding
-keep class androidx.viewbinding.** { *; }
-dontwarn androidx.viewbinding.**

# DataBinding
-keep class androidx.databinding.** { *; }
-dontwarn androidx.databinding.**

# Material Design
-keep class com.google.android.material.** { *; }
-dontwarn com.google.android.material.**

# ConstraintLayout
-keep class androidx.constraintlayout.** { *; }
-dontwarn androidx.constraintlayout.**

# RecyclerView
-keep class androidx.recyclerview.** { *; }
-dontwarn androidx.recyclerview.**

# CardView
-keep class androidx.cardview.** { *; }
-dontwarn androidx.cardview.**

# SwipeRefreshLayout
-keep class androidx.swiperefreshlayout.** { *; }
-dontwarn androidx.swiperefreshlayout.**

# ViewPager2
-keep class androidx.viewpager2.** { *; }
-dontwarn androidx.viewpager2.**

# Fragment
-keep class androidx.fragment.** { *; }
-dontwarn androidx.fragment.**

# AppCompat
-keep class androidx.appcompat.** { *; }
-dontwarn androidx.appcompat.**

# Core
-keep class androidx.core.** { *; }
-dontwarn androidx.core.**

# Activity
-keep class androidx.activity.** { *; }
-dontwarn androidx.activity.**

# Collection
-keep class androidx.collection.** { *; }
-dontwarn androidx.collection.**

# Annotation
-keep class androidx.annotation.** { *; }
-dontwarn androidx.annotation.**

# Arch
-keep class androidx.arch.** { *; }
-dontwarn androidx.arch.**

# Concurrent
-keep class androidx.concurrent.** { *; }
-dontwarn androidx.concurrent.**

# Interpolator
-keep class androidx.interpolator.** { *; }
-dontwarn androidx.interpolator.**

# Loader
-keep class androidx.loader.** { *; }
-dontwarn androidx.loader.**

# LocalBroadcastManager
-keep class androidx.localbroadcastmanager.** { *; }
-dontwarn androidx.localbroadcastmanager.**

# Print
-keep class androidx.print.** { *; }
-dontwarn androidx.print.**

# Transition
-keep class androidx.transition.** { *; }
-dontwarn androidx.transition.**

# VectorDrawable
-keep class androidx.vectordrawable.** { *; }
-dontwarn androidx.vectordrawable.**

# VersionedParcelable
-keep class androidx.versionedparcelable.** { *; }
-dontwarn androidx.versionedparcelable.**

# CustomView
-keep class androidx.customview.** { *; }
-dontwarn androidx.customview.**

# DrawerLayout
-keep class androidx.drawerlayout.** { *; }
-dontwarn androidx.drawerlayout.**

# SlidingPaneLayout
-keep class androidx.slidingpanelayout.** { *; }
-dontwarn androidx.slidingpanelayout.**

# CoordinatorLayout
-keep class androidx.coordinatorlayout.** { *; }
-dontwarn androidx.coordinatorlayout.**

# NestedScrollView
-keep class androidx.core.widget.NestedScrollView { *; }

# الحفاظ على الكلاسات المخصصة للتطبيق
-keep class com.fulkapp.fulk.** { *; }

# الحفاظ على النشاطات والخدمات
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference

# الحفاظ على الطرق المستدعاة من JNI
-keepclasseswithmembernames class * {
    native <methods>;
}

# الحفاظ على الطرق المستدعاة من JavaScript
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# الحفاظ على enum
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# الحفاظ على Parcelable
-keepclassmembers class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator CREATOR;
}

# الحفاظ على Serializable
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# إزالة السجلات في الإنتاج
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# تحذيرات يمكن تجاهلها
-dontwarn java.lang.invoke.*
-dontwarn **$$serializer
-dontwarn javax.**
-dontwarn kotlin.Unit
-dontwarn retrofit2.KotlinExtensions
-dontwarn retrofit2.KotlinExtensions$*
