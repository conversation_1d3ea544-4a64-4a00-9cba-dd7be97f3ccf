import 'package:flutter/material.dart';
// أضف أي استيرادات إضافية هنا عند الحاجة
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';
import 'package:animate_do/animate_do.dart';
import 'package:share_plus/share_plus.dart';
import 'package:file_picker/file_picker.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'package:http/http.dart' as http;
import 'models/store_model.dart';
import 'notifications_page.dart';
import 'appstate.dart';
import 'messenger.dart';
import 'package:fulk/main.dart' show MyApp;
import 'dart:convert';
import 'dart:async';
import 'package:page_transition/page_transition.dart';

// Product class definition
class Product {
  final String type;
  final String thumbnail;
  final double price;
  final String intro;

  Product({
    required this.type,
    required this.thumbnail,
    required this.price,
    required this.intro,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      type: json['type'] ?? 'image',
      thumbnail: json['thumbnail'] ?? 'https://via.placeholder.com/200',
      price: (json['price'] ?? 0).toDouble(),
      intro: json['intro'] ?? 'لا يوجد وصف',
    );
  }
}

// EcommerceApp as the main entry point
class EcommerceApp extends StatelessWidget {
  const EcommerceApp({super.key});

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    return Theme(
      data: MyApp.themes[appState.selectedTheme]?['themeData'] ??
          MyApp.themes['Default']!['themeData'],
      child: const StoresScreen(),
    );
  }
}

// StoresScreen as the main marketplace screen
class StoresScreen extends StatefulWidget {
  const StoresScreen({super.key});

  @override
  State<StoresScreen> createState() => _StoresScreenState();
}

class _StoresScreenState extends State<StoresScreen> {
  Map<String, List<Store>> categorizedStores = {
    'Electronics': [],
    'Fashion': [],
    'Home & Garden': [],
    'Others': [],
  };
  bool isLoading = true;
  String searchQuery = '';
  String selectedCategory = 'All';
  double minRating = 0;
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool hasMore = true;
  String? errorMessage;
  List<Store> storeList = [];

  List<Map<String, dynamic>> notifications = [
    {
      'message': 'خصم 20% على الإلكترونيات اليوم!',
      'timestamp': DateTime.now().toString()
    },
    {
      'message': 'متجر أزياء جديد انضم إلينا!',
      'timestamp': DateTime.now().toString()
    },
    {
      'message': 'تحقق من أحدث الأثاث في قسم المنزل!',
      'timestamp': DateTime.now().toString()
    },
  ];
  int currentNotificationIndex = 0;
  Timer? _notificationTimer;

  @override
  void initState() {
    super.initState();
    final appState = Provider.of<AppState>(context, listen: false);
    if (appState.socket == null) {
      appState.initSocket();
    }
    _setupSocketListeners(appState.socket!);
    _fetchStores();
    _startNotificationCycle();
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        _fetchStores(loadMore: true);
      }
    });
  }

  void _startNotificationCycle() {
    _notificationTimer = Timer.periodic(Duration(seconds: 5), (timer) {
      if (notifications.isNotEmpty) {
        setState(() {
          currentNotificationIndex =
              (currentNotificationIndex + 1) % notifications.length;
        });
      }
    });
  }

  void _setupSocketListeners(io.Socket socket) {
    socket.on('new_store', (data) {
      _fetchStores();
      setState(() {
        notifications.add({
          'message': 'تم إضافة متجر جديد: ${data['name'] ?? 'متجر جديد'}',
          'timestamp': DateTime.now().toString(),
        });
      });
    });
    socket.onDisconnect((_) => print('Disconnected from WebSocket'));
  }

  @override
  void dispose() {
    _notificationTimer?.cancel();
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _fetchStores({bool loadMore = false}) async {
    if (!loadMore) {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });
    }
    final appState = Provider.of<AppState>(context, listen: false);
    try {
      final response = await http.get(
        Uri.parse(
            '${AppState.getBackendUrl()}/api/stores?category=$selectedCategory&search=$searchQuery&minRating=$minRating&page=${loadMore ? ((storeList.length / 10).ceil() + 1) : 1}&limit=10'),
        headers: {'Authorization': 'Bearer ${appState.token}'},
      );
      if (response.statusCode == 200) {
        final body = json.decode(response.body);
        final List<dynamic> storeData = body['data'] ?? [];
        final List<Store> stores =
            storeData.map((data) => Store.fromJson(data)).toList();
        setState(() {
          if (loadMore) {
            storeList.addAll(stores);
          } else {
            storeList = stores;
          }
          hasMore = stores.length == 10;
          isLoading = false;
        });
      } else {
        throw Exception('فشل في جلب المتاجر: ${response.body}');
      }
    } catch (e) {
      setState(() {
        errorMessage = 'خطأ في الاتصال: $e';
        isLoading = false;
      });
    }
  }

  Future<void> _createStore() async {
    final appState = Provider.of<AppState>(context, listen: false);
    final result = await Navigator.push(
      context,
      PageTransition(
        type: PageTransitionType.rightToLeft,
        child: CreateStoreScreen(
          authToken: appState.token ?? '',
          backendUrl: AppState.getBackendUrl(),
        ),
        duration: Duration(milliseconds: 300),
      ),
    );
    if (result == true) {
      await _fetchStores();
      appState.showSnackBar('تم إنشاء المتجر بنجاح!', Colors.green);
    }
  }

  void _showFilterBottomSheet() {
    double tempMinRating = minRating;
    String tempCategory = selectedCategory;
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('تصفية المتاجر',
                      style: Theme.of(context)
                          .textTheme
                          .headlineSmall
                          ?.copyWith(fontFamily: 'Tajawal')),
                  const SizedBox(height: 16),
                  Text('الفئة', style: TextStyle(fontFamily: 'Tajawal')),
                  DropdownButton<String>(
                    value: tempCategory,
                    isExpanded: true,
                    items: [
                      'All',
                      'Electronics',
                      'Fashion',
                      'Home & Garden',
                      'Others'
                    ]
                        .map((category) => DropdownMenuItem(
                              value: category,
                              child: Text(category == 'All' ? 'الكل' : category,
                                  style: TextStyle(fontFamily: 'Tajawal')),
                            ))
                        .toList(),
                    onChanged: (value) =>
                        setModalState(() => tempCategory = value!),
                  ),
                  const SizedBox(height: 16),
                  Text('التقييم الأدنى',
                      style: TextStyle(fontFamily: 'Tajawal')),
                  Slider(
                    value: tempMinRating,
                    min: 0,
                    max: 5,
                    divisions: 5,
                    label: tempMinRating.toStringAsFixed(1),
                    onChanged: (value) =>
                        setModalState(() => tempMinRating = value),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      ElevatedButton(
                        onPressed: () => Navigator.pop(context),
                        style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey),
                        child: Text('إلغاء',
                            style: TextStyle(fontFamily: 'Tajawal')),
                      ),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            minRating = tempMinRating;
                            selectedCategory = tempCategory;
                          });
                          _fetchStores();
                          Navigator.pop(context);
                        },
                        style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.teal[700]),
                        child: Text('تطبيق',
                            style: TextStyle(
                                fontFamily: 'Tajawal', color: Colors.white)),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.teal[700],
        elevation: 0,
        title: Text('المتاجر',
            style:
                TextStyle(fontFamily: 'Tajawal', fontWeight: FontWeight.bold),
            semanticsLabel: 'المتاجر'),
        actions: [
          NotificationsBadge(
            notificationCount:
                notifications.where((n) => !(n['read'] == true)).length,
            onTap: () {
              Scaffold.of(context).openEndDrawer();
            },
          ),
          IconButton(
            icon: Icon(Icons.filter_list, color: Colors.white),
            onPressed: _showFilterBottomSheet,
            tooltip: 'تصفية',
          ),
        ],
      ),
      body: isLoading
          ? Center(child: CircularProgressIndicator())
          : errorMessage != null
              ? Center(
                  child:
                      Text(errorMessage!, style: TextStyle(color: Colors.red)))
              : GridView.builder(
                  controller: _scrollController,
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 1,
                    crossAxisSpacing: 10,
                    mainAxisSpacing: 10,
                  ),
                  itemCount: storeList.length,
                  itemBuilder: (context, index) {
                    return GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          PageTransition(
                            type: PageTransitionType.rightToLeft,
                            child: StoreDetailScreen(store: storeList[index]),
                            duration: Duration(milliseconds: 300),
                          ),
                        );
                      },
                      child: StoreCard(store: storeList[index]),
                    );
                  },
                ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createStore,
        backgroundColor: Colors.teal[700],
        tooltip: 'إنشاء متجر',
        child: const FaIcon(FontAwesomeIcons.store, color: Colors.white),
      ),
      endDrawer: NotificationsDrawer(
        notifications: notifications,
        currentIndex: currentNotificationIndex,
      ),
    );
  }
}

// Notifications Drawer
class NotificationsDrawer extends StatelessWidget {
  final List<Map<String, dynamic>> notifications;
  final int currentIndex;

  const NotificationsDrawer(
      {super.key, required this.notifications, required this.currentIndex});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.teal[700]!, Colors.teal[300]!],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          children: [
            DrawerHeader(
              child: Text(
                'الإشعارات',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontFamily: 'Tajawal',
                ),
              ),
            ),
            Expanded(
              child: notifications.isEmpty
                  ? Center(
                      child: Text(
                        'لا توجد إشعارات جديدة',
                        style: TextStyle(
                            color: Colors.white, fontFamily: 'Tajawal'),
                      ),
                    )
                  : FadeIn(
                      child: ListTile(
                        title: Text(
                          notifications[currentIndex]['message'],
                          style: TextStyle(
                              color: Colors.white, fontFamily: 'Tajawal'),
                        ),
                        subtitle: Text(
                          notifications[currentIndex]['timestamp']
                              .substring(0, 16),
                          style: TextStyle(
                              color: Colors.white70, fontFamily: 'Tajawal'),
                        ),
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }
}

// NotificationsBadge widget for showing unread notification count in the AppBar
class NotificationsBadge extends StatelessWidget {
  final int notificationCount;
  final VoidCallback onTap;
  const NotificationsBadge(
      {Key? key, required this.notificationCount, required this.onTap})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        IconButton(
          icon: Icon(Icons.notifications,
              color: Colors.white, semanticLabel: 'الإشعارات'),
          onPressed: onTap,
        ),
        if (notificationCount > 0)
          Positioned(
            right: 8,
            top: 8,
            child: Container(
              padding: EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(10),
              ),
              constraints: BoxConstraints(minWidth: 20, minHeight: 20),
              child: Text(
                '$notificationCount',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontFamily: 'Tajawal',
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                semanticsLabel: 'عدد الإشعارات الجديدة',
              ),
            ),
          ),
      ],
    );
  }
}

// StoreCard for displaying individual stores
class StoreCard extends StatelessWidget {
  final Store store;

  const StoreCard({super.key, required this.store});

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          PageTransition(
            type: PageTransitionType.rightToLeft,
            child: StoreDetailScreen(store: store),
            duration: Duration(milliseconds: 300),
          ),
        );
      },
      child: Hero(
        tag: 'store_${store.id}',
        child: Card(
          elevation: 8,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Stack(
                children: [
                  ClipRRect(
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(16)),
                    child: CachedNetworkImage(
                      imageUrl: store.products.isNotEmpty
                          ? store.products[0].thumbnail
                          : 'https://via.placeholder.com/200',
                      height: 140,
                      width: double.infinity,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Shimmer.fromColors(
                        baseColor: Colors.grey[300]!,
                        highlightColor: Colors.grey[100]!,
                        child: Container(
                          height: 140,
                          color: Colors.white,
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        height: 140,
                        color: Colors.grey[400],
                        child: const FaIcon(FontAwesomeIcons.image,
                            size: 50, color: Colors.white),
                      ),
                    ),
                  ),
                  Positioned(
                    top: 8,
                    right: 8,
                    child: IconButton(
                      icon: Icon(
                        store.isFavorited
                            ? Icons.favorite
                            : Icons.favorite_border,
                        color: store.isFavorited ? Colors.red : Colors.white,
                      ),
                      onPressed: () {
                        appState.toggleFavoriteStore(store.id);
                        store.isFavorited = !store.isFavorited;
                        (context as Element).markNeedsBuild();
                      },
                    ),
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      store.name,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Tajawal',
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 6),
                    Text(
                      store.description,
                      style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                          fontFamily: 'Tajawal'),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        RatingBarIndicator(
                          rating: store.rating,
                          itemBuilder: (context, _) => const FaIcon(
                            FontAwesomeIcons.star,
                            color: Colors.amber,
                          ),
                          itemCount: 5,
                          itemSize: 16.0,
                          direction: Axis.horizontal,
                        ),
                        const SizedBox(width: 8),
                        Chip(
                          label: Text(
                            '${store.products.length} منتج',
                            style: TextStyle(fontFamily: 'Tajawal'),
                          ),
                          backgroundColor: Colors.teal[100],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// CreateStoreScreen for adding new stores
class CreateStoreScreen extends StatefulWidget {
  final String authToken;
  final String backendUrl;

  const CreateStoreScreen(
      {super.key, required this.authToken, required this.backendUrl});

  @override
  State<CreateStoreScreen> createState() => _CreateStoreScreenState();
}

class _CreateStoreScreenState extends State<CreateStoreScreen> {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController descController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  String selectedCategory = 'Electronics';
  List<Map<String, dynamic>> products = [];
  bool isLoading = false;

  Future<String?> _uploadFile(file) async {
    try {
      var request = http.MultipartRequest(
          'POST', Uri.parse('${widget.backendUrl}/api/upload'));
      request.headers['Authorization'] = 'Bearer ${widget.authToken}';
      request.files.add(await http.MultipartFile.fromPath('file', file.path));
      var response = await request.send();
      if (response.statusCode == 200) {
        final responseBody = await response.stream.bytesToString();
        return jsonDecode(responseBody)['url'];
      } else {
        throw Exception('فشل في رفع الملف: ${response.reasonPhrase}');
      }
    } catch (e) {
      context.read<AppState>().showSnackBar('خطأ في رفع الملف: $e', Colors.red);
      return null;
    }
  }

  Future<void> _addProduct() async {
    FilePickerResult? result =
        await FilePicker.platform.pickFiles(type: FileType.media);
    if (result != null && result.files.single.path != null) {
      String path = result.files.single.path!;
      String type = path.endsWith('.mp4') ? 'video' : 'image';
      final priceController = TextEditingController();
      String? introText;

      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('إضافة تفاصيل المنتج',
              style: TextStyle(fontFamily: 'Tajawal')),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: priceController,
                decoration: const InputDecoration(
                    labelText: 'السعر',
                    hintStyle: TextStyle(fontFamily: 'Tajawal')),
                keyboardType: TextInputType.number,
              ),
              TextField(
                onChanged: (value) => introText = value,
                decoration: const InputDecoration(
                    labelText: 'الوصف',
                    hintStyle: TextStyle(fontFamily: 'Tajawal')),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child:
                  const Text('إلغاء', style: TextStyle(fontFamily: 'Tajawal')),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child:
                  const Text('إضافة', style: TextStyle(fontFamily: 'Tajawal')),
            ),
          ],
        ),
      );

      if (confirmed == true && priceController.text.isNotEmpty) {
        final file = (path);
        final uploadedUrl = await _uploadFile(file);
        if (uploadedUrl != null) {
          setState(() {
            products.add({
              'type': type,
              'file': file,
              'thumbnail': uploadedUrl,
              'price': double.parse(priceController.text),
              'intro': introText ?? 'لا يوجد وصف',
            });
          });
        }
      }
    }
  }

  Future<void> _createStore() async {
    if (nameController.text.isEmpty || phoneController.text.isEmpty) {
      context
          .read<AppState>()
          .showSnackBar('يرجى ملء جميع الحقول المطلوبة', Colors.red);
      return;
    }

    setState(() => isLoading = true);
    var request = http.MultipartRequest(
        'POST', Uri.parse('${widget.backendUrl}/api/stores'));
    request.headers['Authorization'] = 'Bearer ${widget.authToken}';
    request.fields['name'] = nameController.text;
    request.fields['description'] = descController.text;
    request.fields['category'] = selectedCategory;
    request.fields['phone'] = phoneController.text;
    request.fields['products'] = jsonEncode(products
        .map((p) => {
              'type': p['type'],
              'thumbnail': p['thumbnail'],
              'price': p['price'],
              'intro': p['intro'],
            })
        .toList());

    try {
      var response = await request.send();
      if (response.statusCode == 201) {
        Navigator.pop(context, true);
      } else {
        final responseBody = await response.stream.bytesToString();
        throw Exception('فشل في إنشاء المتجر: $responseBody');
      }
    } catch (e) {
      context.read<AppState>().showSnackBar('خطأ في الاتصال: $e', Colors.red);
    } finally {
      setState(() => isLoading = false);
    }
  }

  @override
  void dispose() {
    nameController.dispose();
    descController.dispose();
    phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          title: const Text('إنشاء متجرك',
              style: TextStyle(fontFamily: 'Tajawal'))),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            ZoomIn(
              child: TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'اسم المتجر',
                  hintStyle: TextStyle(fontFamily: 'Tajawal'),
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(height: 16),
            ZoomIn(
              child: TextField(
                controller: descController,
                decoration: const InputDecoration(
                  labelText: 'الوصف',
                  hintStyle: TextStyle(fontFamily: 'Tajawal'),
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(height: 16),
            ZoomIn(
              child: TextField(
                controller: phoneController,
                decoration: const InputDecoration(
                  labelText: 'رقم التواصل',
                  hintStyle: TextStyle(fontFamily: 'Tajawal'),
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.phone,
              ),
            ),
            const SizedBox(height: 16),
            ZoomIn(
              child: DropdownButtonFormField<String>(
                value: selectedCategory,
                onChanged: (value) => setState(() => selectedCategory = value!),
                items: ['Electronics', 'Fashion', 'Home & Garden', 'Others']
                    .map((category) => DropdownMenuItem(
                          value: category,
                          child: Text(category,
                              style: TextStyle(fontFamily: 'Tajawal')),
                        ))
                    .toList(),
                decoration: const InputDecoration(
                  labelText: 'الفئة',
                  hintStyle: TextStyle(fontFamily: 'Tajawal'),
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            const SizedBox(height: 20),
            ZoomIn(
              child: ElevatedButton.icon(
                onPressed: _addProduct,
                icon: const Icon(Icons.add),
                label: const Text('إضافة منتج',
                    style: TextStyle(fontFamily: 'Tajawal')),
                style:
                    ElevatedButton.styleFrom(backgroundColor: Colors.teal[700]),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 120,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: products.length,
                itemBuilder: (context, index) => Padding(
                  padding: const EdgeInsets.all(8),
                  child: Column(
                    children: [
                      CachedNetworkImage(
                        imageUrl: products[index]['thumbnail'],
                        width: 80,
                        height: 80,
                        fit: BoxFit.cover,
                        errorWidget: (context, url, error) =>
                            const Icon(Icons.error),
                      ),
                      Text('${products[index]['price']} ريال',
                          style: TextStyle(fontFamily: 'Tajawal')),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
            isLoading
                ? const CircularProgressIndicator(color: Colors.teal)
                : ZoomIn(
                    child: ElevatedButton.icon(
                      onPressed: _createStore,
                      icon: const Icon(Icons.check),
                      label: const Text('إنشاء المتجر',
                          style: TextStyle(fontFamily: 'Tajawal')),
                      style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.teal[700]),
                    ),
                  ),
          ],
        ),
      ),
    );
  }
}

// StoreDetailScreen for viewing store details
class StoreDetailScreen extends StatefulWidget {
  final Store store;

  const StoreDetailScreen({super.key, required this.store});

  @override
  State<StoreDetailScreen> createState() => _StoreDetailScreenState();
}

class _StoreDetailScreenState extends State<StoreDetailScreen> {
  late List<bool> likedProducts;

  @override
  void initState() {
    super.initState();
    likedProducts = List.generate(widget.store.products.length, (_) => false);
  }

  void _shareStore() {
    Share.share(
      'تحقق من متجر "${widget.store.name}" مع ${widget.store.products.length} منتجات رائعة! التواصل: ${widget.store.phone}',
      subject: 'استكشف متجري الإلكتروني',
    );
  }

  void _contactStoreOwner(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    Navigator.push(
      context,
      PageTransition(
        type: PageTransitionType.rightToLeft,
        child: ChatPage(
          recipientId: widget.store.id,
          userName: widget.store.name,
          currentUserId: appState.username ?? 'unknown',
          socket: appState.socket!,
        ),
        duration: Duration(milliseconds: 300),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.store.name, style: TextStyle(fontFamily: 'Tajawal')),
        actions: [
          IconButton(
            icon: Icon(
              widget.store.isFavorited ? Icons.favorite : Icons.favorite_border,
              color: Colors.white,
            ),
            onPressed: () {
              appState.toggleFavoriteStore(widget.store.id);
              widget.store.isFavorited = !widget.store.isFavorited;
              (context as Element).markNeedsBuild();
            },
          ),
          IconButton(
            icon: const FaIcon(FontAwesomeIcons.shareAlt, color: Colors.white),
            onPressed: _shareStore,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ZoomIn(
              child: Card(
                elevation: 8,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16)),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'تفاصيل المتجر',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'Tajawal',
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        widget.store.description,
                        style: TextStyle(fontSize: 16, fontFamily: 'Tajawal'),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(Icons.phone, color: Colors.teal[700]),
                          const SizedBox(width: 8),
                          Text(
                            'التواصل: ${widget.store.phone}',
                            style:
                                TextStyle(fontSize: 16, fontFamily: 'Tajawal'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          RatingBarIndicator(
                            rating: widget.store.rating,
                            itemBuilder: (context, _) => const FaIcon(
                              FontAwesomeIcons.star,
                              color: Colors.amber,
                            ),
                            itemCount: 5,
                            itemSize: 20.0,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '${widget.store.rating.toStringAsFixed(1)}',
                            style: TextStyle(fontFamily: 'Tajawal'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: () => _contactStoreOwner(context),
                        icon: const Icon(Icons.message),
                        label: const Text('تواصل مع المالك',
                            style: TextStyle(fontFamily: 'Tajawal')),
                        style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.teal[700]),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'المنتجات',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                fontFamily: 'Tajawal',
              ),
            ),
            ListView.builder(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              itemCount: widget.store.products.length,
              itemBuilder: (context, index) {
                final product = widget.store.products[index];
                return FadeInUp(
                  duration: Duration(milliseconds: 300 + (index * 100)),
                  child: product.type == 'video'
                      ? VideoProductCard(
                          videoUrl: product.thumbnail,
                          price: product.price,
                          intro: product.intro,
                          isLiked: likedProducts[index],
                          onLike: () => setState(() =>
                              likedProducts[index] = !likedProducts[index]),
                        )
                      : ImageProductCard(
                          imageUrl: product.thumbnail,
                          price: product.price,
                          intro: product.intro,
                          isLiked: likedProducts[index],
                          onLike: () => setState(() =>
                              likedProducts[index] = !likedProducts[index]),
                        ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

// ImageProductCard for image-based products
class ImageProductCard extends StatelessWidget {
  final String imageUrl;
  final double price;
  final String intro;
  final bool isLiked;
  final VoidCallback onLike;

  const ImageProductCard({
    super.key,
    required this.imageUrl,
    required this.price,
    required this.intro,
    required this.isLiked,
    required this.onLike,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 8,
      child: Column(
        children: [
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
                child: CachedNetworkImage(
                  imageUrl: imageUrl,
                  height: 200,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Shimmer.fromColors(
                    baseColor: Colors.grey[300]!,
                    highlightColor: Colors.grey[100]!,
                    child: Container(
                      height: 200,
                      color: Colors.white,
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    height: 200,
                    color: Colors.grey,
                    child: const Icon(Icons.error),
                  ),
                ),
              ),
              Positioned(
                top: 8,
                right: 8,
                child: IconButton(
                  icon: FaIcon(
                    isLiked
                        ? FontAwesomeIcons.solidHeart
                        : FontAwesomeIcons.heart,
                    color: isLiked ? Colors.red : Colors.white,
                  ),
                  onPressed: onLike,
                ),
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  intro,
                  style: const TextStyle(fontSize: 16, fontFamily: 'Tajawal'),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Text(
                  '${price.toStringAsFixed(2)} ريال',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.teal[700],
                    fontFamily: 'Tajawal',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// VideoProductCard for video-based products
class VideoProductCard extends StatefulWidget {
  final String videoUrl;
  final double price;
  final String intro;
  final bool isLiked;
  final VoidCallback onLike;

  const VideoProductCard({
    super.key,
    required this.videoUrl,
    required this.price,
    required this.intro,
    required this.isLiked,
    required this.onLike,
  });

  @override
  State<VideoProductCard> createState() => _VideoProductCardState();
}

class _VideoProductCardState extends State<VideoProductCard> {
  late VideoPlayerController _controller;
  late ChewieController _chewieController;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl))
      ..initialize().then((_) {
        setState(() {});
      }).catchError((error) {
        setState(() => _hasError = true);
      });
    _chewieController = ChewieController(
      videoPlayerController: _controller,
      autoPlay: false,
      looping: false,
      errorBuilder: (context, errorMessage) => Container(
        height: 200,
        color: Colors.grey,
        child: const Center(child: Icon(Icons.error, color: Colors.white)),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    _chewieController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 8,
      child: Column(
        children: [
          Stack(
            children: [
              _hasError || !_controller.value.isInitialized
                  ? Container(
                      height: 200,
                      color: Colors.grey,
                      child: const Center(child: Icon(Icons.error)),
                    )
                  : SizedBox(
                      height: 200,
                      child: Chewie(controller: _chewieController),
                    ),
              Positioned(
                top: 8,
                right: 8,
                child: IconButton(
                  icon: FaIcon(
                    widget.isLiked
                        ? FontAwesomeIcons.solidHeart
                        : FontAwesomeIcons.heart,
                    color: widget.isLiked ? Colors.red : Colors.white,
                  ),
                  onPressed: widget.onLike,
                ),
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.intro,
                  style: const TextStyle(fontSize: 16, fontFamily: 'Tajawal'),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Text(
                  '${widget.price.toStringAsFixed(2)} ريال',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.teal[700],
                    fontFamily: 'Tajawal',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
