import 'package:flutter/material.dart';
import 'share_borrow_features_screen.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'id16.dart';
import 'id25.dart';
import 'id26.dart';
import 'appstate.dart';
import 'id22.dart';
import 'package:fulk/id1.dart' show ProfessionsListPage;
import 'id20.dart';
import 'id2.dart';
import 'id27.dart';
import 'id19.dart';
import 'id18.dart';
import 'id23.dart';
import 'id21.dart';
import 'id17.dart';
import 'package:fulk/id3.dart';
import 'package:fulk/id4.dart';
import 'package:fulk/id5.dart';
import 'package:fulk/id6.dart';
import 'package:fulk/id7.dart';
import 'package:fulk/id8.dart';
import 'package:fulk/id9.dart';
import 'package:fulk/id10.dart';
import 'package:fulk/id11.dart';
import 'package:fulk/id12.dart';
import 'package:fulk/id13.dart';
import 'package:fulk/id14.dart';
import 'id15.dart';
import 'id24.dart';

// Constants for card animations

// Error messages
const String networkError = 'Network error occurred';
const String loadingError = 'Error loading data';

// Card cache manager
final cardCacheManager = CacheManager(
  Config(
    'card_cache',
    stalePeriod: const Duration(days: 7),
    maxNrOfCacheObjects: 50,
    repo: JsonCacheInfoRepository(databaseName: 'card_cache_db'),
    fileService: HttpFileService(),
  ),
);

// نموذج البطاقة
class New {
  final String id;
  final String image;
  final String title;
  final String description;

  const New({
    required this.id,
    required this.image,
    required this.title,
    required this.description,
  });

  factory New.fromJson(Map<String, dynamic> json) {
    return New(
      id: json['id'] ?? '',
      image: json['image'] ?? 'https://via.placeholder.com/200',
      title: json['title'] ?? 'بدون عنوان',
      description: json['description'] ?? 'بدون وصف',
    );
  }
}

class CardsApp extends StatelessWidget {
  const CardsApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: PerfectWorldScreen(),
    );
  }
}

class PerfectWorldScreen extends StatefulWidget {
  const PerfectWorldScreen({super.key});

  @override
  State<PerfectWorldScreen> createState() => _PerfectWorldScreenState();
}

class _PerfectWorldScreenState extends State<PerfectWorldScreen>
    with TickerProviderStateMixin {
  // بيانات محلية ثابتة للبطاقات
  final List<New> cards = [
    const New(
      id: '1',
      image: 'https://via.placeholder.com/200',
      title: 'أفق المعرفة',
      description:
          'رحلةٌ تتجاوز الحدود، حيث تتفتح العقول وتتشكل المستقبلات بقوة العلم والمهارة.',
    ),
    const New(
      id: '2',
      image: 'https://via.placeholder.com/200',
      title: 'جسور الفرص',
      description:
          'حيث تلتقي الطموحات بالإمكانيات،وتفتح أبواب الإنجاز لأصحاب الرؤية.',
    ),
    const New(
      id: '3',
      image: 'https://via.placeholder.com/200',
      title: 'نسيج الأفكار',
      description: 'حيث تتلاقى العقول المبدعة لتصنع أحلامًا جماعية تتحقق.',
    ),
    const New(
      id: '4',
      image: 'https://via.placeholder.com/200',
      title: 'نور التكافل',
      description: 'حيث يتحول عطاؤك إلى شعاع أمل',
    ),
    const New(
      id: '5',
      image: 'https://via.placeholder.com/200',
      title: 'ميثاق الود',
      description:
          'حيث تتلاقى الأرواح بنية صادقة لتبني حياة مشتركة ملؤها المودة والرحمة.لبناء أسرة متماسكة.',
    ),
    const New(
      id: '6',
      image: 'https://via.placeholder.com/200',
      title: 'أثر الضائع',
      description: 'نضيء دروب البحث عن المفقودات ونعيد الابتسامة لكل قلب.',
    ),
    const New(
      id: '7',
      image: 'https://via.placeholder.com/200',
      title: 'سوق الثقة',
      description:
          'نربط بين البائع والمشتري بسهولة وشفافية,لتحويل الأغراض إلى قصص نجاح مشتركة.',
    ),
    const New(
      id: '8',
      image: 'https://via.placeholder.com/200',
      title: 'ركن الموهبة',
      description: 'حيث تتفتح الهوايات وتتحول الأعمال الحرفية إلى روائع يومية.',
    ),
    const New(
      id: '9',
      image: 'https://via.placeholder.com/200',
      title: 'ميدان الابتكار',
      description:
          'فضاء يجمع العقول لتبادل الأفكار وتحويلها إلى واقع يعزز التقدم.',
    ),
    const New(
      id: '10',
      image: 'https://via.placeholder.com/200',
      title: 'ملتقى المهارة',
      description:
          'منصة تربط الحرفيين المبدعين بالعملاء الذين يقدرون المهارة والإتقان.',
    ),
    const New(
      id: '11',
      image: 'https://via.placeholder.com/200',
      title: 'وصلة',
      description: 'تواصلوا وشاركوا قصص قراكم ومدنكم بسهولة وفرح في مكان واحد',
    ),
    const New(
      id: '12',
      image: 'https://via.placeholder.com/200',
      title: 'نور الرعاية',
      description:
          'مجتمع يضيء طريق الصحة، يربط الناس بالمعلومات والدعم لمواجهة الأمراض بثقة.',
    ),
    const New(
      id: '13',
      image: 'https://via.placeholder.com/200',
      title: 'نبض الطبيعة',
      description:
          'بوابة تفتح أبواب الدهشة، تربطنا بتناغم الحياة بيننا وبين ما حولنا.',
    ),
    const New(
      id: '14',
      image: 'https://via.placeholder.com/200',
      title: 'أفق الرؤية',
      description:
          'منصة تجمع خطب وتصريحات العلماء والسياسيين ورؤساء الدول لإلهام العقول وتوثيق التاريخ.',
    ),
    const New(
      id: '15',
      image: 'https://via.placeholder.com/200',
      title: 'فريقي',
      description:
          'منصتك التي تجمع مشجعي الأندية الرياضية لمشاركة الشغف، مناقشة المباريات، والاحتفال بالانتصارات معًا!',
    ),
    const New(
      id: '16',
      image: 'https://cdn-icons-png.flaticon.com/512/616/616408.png',
      title: 'عالم تربية الطيور',
      description:
          'ملتقى رقمي يجمع مربي الطيور لمشاركة شغف الرعاية، تبادل المعرفة، والاحتفال بنجاح التربية.',
    ),
    const New(
      id: '17',
      image: 'https://via.placeholder.com/200',
      title: 'عالم تربيه النباتات',
      description:
          'ملتقى رقمي يجمع مربي النباتات لمشاركة شغف الرعاية، تبادل المعرفة، والاحتفال بنجاح التربية.',
    ),
    const New(
      id: '18',
      image: 'https://cdn-icons-png.flaticon.com/512/616/616408.png',
      title: 'عالم تربية الحيوانات',
      description:
          'ملتقى رقمي يجمع مربي الحيوانات لمشاركة شغف الرعاية، تبادل المعرفة، والاحتفال بنجاح التربية.',
    ),
    const New(
      id: '19',
      image: 'https://cdn-icons-png.flaticon.com/512/616/616408.png',
      title: 'عالم تربية الاسماك',
      description:
          'ملتقى رقمي يجمع مربي الاسماك لمشاركة شغف الرعاية، تبادل المعرفة، والاحتفال بنجاح التربية.',
    ),
    const New(
      id: '20',
      image: 'https://cdn-icons-png.flaticon.com/512/616/616408.png',
      title: 'الصناعات الغذائية',
      description: 'كل ما تحتاج معرفته عن تصنيع المواد الغذائية المنزلية.',
    ),
    const New(
      id: '21',
      image: 'https://cdn-icons-png.flaticon.com/512/616/616408.png',
      title: 'صيانة الاجهزة الكهربائية والالكترونية',
      description:
          'كل ما تحتاج معرفته عن صيانة الاجهزة الكهربائية والالكترونية.',
    ),
    const New(
      id: '22',
      image: 'https://cdn-icons-png.flaticon.com/512/616/616408.png',
      title: 'بيت المشاركة',
      description:
          ' ملتقى رقمي يربط الأفراد لمشاركة الأدوات والكتب، مما يعزز الإبداع ويقلل الهدر بروح الاستدامة.',
    ),
    const New(
      id: '23',
      image: 'https://cdn-icons-png.flaticon.com/512/616/616408.png',
      title: 'يد العابر',
      description:
          'مجتمع رقمي يوحد القلوب لتقديم المساعدة على الطريق وخدمات التوصيل بتكلفة رمزية، لبناء طريق مليء بالعطاء.',
    ),
    const New(
      id: '24',
      image: 'https://cdn-icons-png.flaticon.com/512/616/616408.png',
      title: 'رحلة الاكتشاف والمعرفة',
      description:
          'منصة تجمع بين التعلم والمرح، تقدم فيديوهات وأدوات تعليمية تُشعل خيال الأطفال وتُثري معرفتهم.',
    ),
    const New(
      id: '25',
      image: 'https://cdn-icons-png.flaticon.com/512/616/616408.png',
      title: 'نور الأخوة',
      description:
          'من صلى صلاتنا، واستقبل قبلتنا، وأكل ذبيحتنا فذلك المسلم  الذي له ذمة الله وذمة رسول الله صلى الله عليه وسلم، فلا تخفروا الله في ذمته .',
    ),
    const New(
      id: '26',
      image: 'https://cdn-icons-png.flaticon.com/512/616/616408.png',
      title: 'ومضة',
      description:
          'منصة الريلز التي تتيح لك مشاركة لحظاتك الإبداعية في مقاطع فيديو قصيرة تأسر القلوب وتلهم المجتمع.',
    ),
    // أضف المزيد من البطاقات حسب الحاجة
    const New(
      id: '27',
      image: 'https://cdn-icons-png.flaticon.com/512/616/616408.png',
      title: 'Z',
      description: 'منصة Z للتواصل السريع.',
    ),
  ];

  bool isLoading = false; // لإزالة حالة التحميل
  double scale = 1.0;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);
    _fadeAnimation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  void _navigateToCardScreen(BuildContext context, New card) {
    Widget? screen;
    switch (card.id) {
      case '1':
        screen = const ProfessionsListPage();
        break;
      case '2':
        screen = const Rayan2();
        break;
      case '3':
        screen = const Rayan3();
        break;
      case '4':
        screen = const Rayan4();
        break;
      case '5':
        screen = const Rayan5();
        break;
      case '6':
        screen = const Rayan6();
        break;
      case '7':
        screen = const Rayan7();
        break;
      case '8':
        screen = const Rayan8();
        break;
      case '9':
        screen = const Rayan9();
        break;
      case '10':
        screen = const Rayan10();
        break;
      case '11':
        screen = const Rayan11();
        break;
      case '12':
        screen = const Rayan12();
        break;
      case '13':
        screen = const Rayan13();
        break;
      case '14':
        screen = const Rayan14();
        break;
      case '15':
        screen = const Rayan15();
        break;
      case '16':
        screen = const BirdBreedingPage();
        break;
      case '17':
        screen = const AgriculturalPlantsPage();
        break;
      case '18':
        screen = const AnimalBreedingPage();
        break;
      case '19':
        screen = const FishBreedingPage();
        break;
      case '20':
        screen = const HomeFoodIndustriesPage();
        break;
      case '21':
        screen = const DeviceMaintenancePage();
        break;
      case '22':
        screen = const ShareBorrowScreen();
        break;
      case '23':
        screen = const RideServicePage();
        break;
      case '24':
        screen = const ChildCareApp();
        break;
      case '25':
        screen = const GroupsPage();
        break;
      case '26':
        screen = ReelsScreen(onToggleTheme: () {
          // TODO: Implement or connect theme toggling logic
          // Example: context.read<AppState>().toggleTheme();
        });
        break;
      case '27':
        screen = XFeedPage();
        break;
      default:
        // Use post-frame callback to show snackbar after build phase
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (context.mounted) {
            context.read<AppState>().showSnackBar(
                'هذا العالم غير متاح حاليًا: ${card.id}', Colors.red);
          }
        });
        return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => screen!,
        settings: RouteSettings(arguments: card),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF1A0B3B),
            Color(0xFF3C1E7A),
            Color(0xFF6B3FA0),
          ],
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(child: _buildCardsGrid()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20.0, horizontal: 16.0),
      child: Column(
        children: [
          AnimatedTextKit(
            animatedTexts: [
              TypewriterAnimatedText(
                'عالمك المثالي',
                textStyle: const TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  shadows: [
                    Shadow(
                      blurRadius: 10.0,
                      color: Colors.purpleAccent,
                      offset: Offset(0, 0),
                    ),
                  ],
                ),
                speed: const Duration(milliseconds: 100),
              ),
            ],
            isRepeatingAnimation: true,
            repeatForever: true,
          ),
          const SizedBox(height: 10),
          FadeTransition(
            opacity: _fadeAnimation,
            child: const Text(
              'انشر علما يعش خيرك ابدا...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white70,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardsGrid() {
    return GridView.builder(
      padding: const EdgeInsets.all(16.0),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.7,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: cards.length,
      itemBuilder: (context, index) => _buildCard(cards[index]),
    );
  }

  Widget _buildCard(New card) {
    return GestureDetector(
      onTapDown: (_) => setState(() => scale = 0.95),
      onTapUp: (_) {
        setState(() => scale = 1.0);
        _navigateToCardScreen(context, card);
      },
      onTapCancel: () => setState(() => scale = 1.0),
      child: AnimatedScale(
        scale: scale,
        duration: const Duration(milliseconds: 200),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.purple.withOpacity(0.3),
                Colors.blue.withOpacity(0.3),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.purpleAccent.withAlpha((0.4 * 255).round()),
                blurRadius: 10,
                spreadRadius: 2,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(flex: 3, child: _buildCardImage(card)),
              Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      card.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            blurRadius: 5.0,
                            color: Colors.purpleAccent,
                            offset: Offset(0, 0),
                          ),
                        ],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 6),
                    Text(
                      card.description,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white.withAlpha((0.8 * 255).round()),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 10),
                    _buildExploreButton(context, card),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCardImage(New card) {
    return ClipRRect(
      borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      child: Stack(
        children: [
          CachedNetworkImage(
            imageUrl: card.image,
            fit: BoxFit.cover,
            imageBuilder: (context, imageProvider) => Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: imageProvider,
                  fit: BoxFit.cover,
                  alignment: Alignment.center,
                ),
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(16)),
              ),
            ),
            placeholder: (context, url) => const Center(
              child: SpinKitDoubleBounce(
                color: Colors.purpleAccent,
                size: 40.0,
              ),
            ),
            errorWidget: (context, url, error) => Container(
              color: Colors.grey.withAlpha((0.3 * 255).round()),
              child: const Icon(
                Icons.broken_image,
                size: 50,
                color: Colors.white70,
              ),
            ),
          ),
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withAlpha((0.5 * 255).round()),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExploreButton(BuildContext context, New card) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Colors.purpleAccent, Colors.blueAccent],
        ),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.purpleAccent.withAlpha((0.5 * 255).round()),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ElevatedButton.icon(
        icon: const Icon(Icons.explore, size: 20, color: Colors.white),
        label: const Text(
          'استكشف',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        onPressed: () => _navigateToCardScreen(context, card),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
      ),
    );
  }
}
