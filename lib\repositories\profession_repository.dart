import '../../models/profession_model.dart';
import '../../services/api_service.dart';
import 'package:fulk/models/profession_model.dart' show DemandLevel;

class ProfessionRepository {
  final ApiService _apiService = ApiService();
  final String baseUrl = 'https://api.example.com'; // Replace with actual API base URL

  Future<ProfessionResponse> getProfessionsByType(String type) async {
    try {
      final response = await _apiService.get('$baseUrl/professions?type=$type', parser: (data) {
        final List<dynamic> professionData = data['data'] ?? [];
        return professionData.map((json) => Profession.fromJson(json)).toList();
      });
      
      if (response.isSuccess) {
        return ProfessionResponse(isSuccess: true, data: response.data);
      } else {
        return ProfessionResponse(
          isSuccess: false,
          message: 'Failed to fetch professions: ${response.message}',
        );
      }
    } catch (e) {
      print('Error fetching professions: $e');
      // Fallback to local data (mock example)
      return ProfessionResponse(
        isSuccess: true,
        data: _getLocalProfessions(type),
      );
    }
  }

  List<Profession> _getLocalProfessions(String type) {
    // Mock data for offline fallback
    if (type == 'technical') {
      return [
        Profession(
          id: '1',
          title: 'مهندس برمجيات',
          description: 'تطوير تطبيقات الويب والموبايل',
          image: 'assets/images/software_engineer.jpg',
          isLocal: true,
          averageSalary: 8000,
          demand: DemandLevel.high,
          requiredSkills: ['Dart', 'Flutter', 'JavaScript'],
        ),
      ];
    } else if (type == 'creative') {
      return [
        Profession(
          id: '2',
          title: 'مصمم جرافيك',
          description: 'تصميم الشعارات والواجهات',
          image: 'assets/images/graphic_designer.jpg',
          isLocal: true,
          averageSalary: 6000,
          demand: DemandLevel.medium,
          requiredSkills: ['Photoshop', 'Illustrator'],
        ),
      ];
    } else if (type == 'service') {
      return [
        Profession(
          id: '3',
          title: 'طباخ',
          description: 'إعداد الوجبات في المطاعم',
          image: 'assets/images/chef.jpg',
          isLocal: true,
          averageSalary: 4000,
          demand: DemandLevel.high,
          requiredSkills: ['طهي', 'إدارة المطبخ'],
        ),
      ];
    }
    return [];
  }
}

class ProfessionResponse {
  final bool isSuccess;
  final List<Profession>? data;
  final String? message;

  ProfessionResponse({
    required this.isSuccess,
    this.data,
    this.message,
  });
}