# ========================================
# إعدادات الإنتاج لتطبيق فلك
# ========================================

# إعدادات التطبيق
NODE_ENV=production
PORT=443
HOST=0.0.0.0
DOMAIN=fulkapp.com
API_URL=https://api.fulkapp.com

# إعدادات قاعدة البيانات
MONGODB_URI=mongodb+srv://fulk_user:<EMAIL>/fulk_production?retryWrites=true&w=majority

# إعدادات JWT (يجب تغيير هذه المفاتيح)
JWT_SECRET=CHANGE_THIS_TO_STRONG_SECRET_KEY_FOR_PRODUCTION_MIN_32_CHARS
JWT_EXPIRES_IN=7d
JWT_COOKIE_EXPIRES_IN=7

# إعدادات CORS
CLIENT_URL=https://fulkapp.com
FRONTEND_URL=https://fulkapp.com
ALLOWED_ORIGINS=https://fulkapp.com,https://www.fulkapp.com,https://admin.fulkapp.com

# إعدادات WhatsApp Business API
WHATSAPP_API_URL=https://graph.facebook.com/v18.0
WHATSAPP_ACCESS_TOKEN=CHANGE_THIS_TO_YOUR_WHATSAPP_BUSINESS_TOKEN
WHATSAPP_PHONE_NUMBER_ID=CHANGE_THIS_TO_YOUR_PHONE_NUMBER_ID
WHATSAPP_VERIFY_TOKEN=CHANGE_THIS_TO_YOUR_VERIFY_TOKEN

# إعدادات Supabase
SUPABASE_URL=https://gfgdgbjkorzpjewrjnhc.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdmZ2RnYmprb3J6cGpld3JqbmhjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcxNTk2ODQsImV4cCI6MjA2MjczNTY4NH0.Rr2y0rzdghPnHp0QAD-edhJLZ9EXHRKWL9wtLiCIHZs

# إعدادات Firebase (اختياري)
FIREBASE_API_KEY=your_production_firebase_api_key
FIREBASE_AUTH_DOMAIN=your_production_firebase_auth_domain
FIREBASE_PROJECT_ID=your_production_firebase_project_id
FIREBASE_STORAGE_BUCKET=your_production_firebase_storage_bucket
FIREBASE_MESSAGING_SENDER_ID=your_production_firebase_messaging_sender_id
FIREBASE_APP_ID=your_production_firebase_app_id

# إعدادات التخزين (اختياري)
AWS_ACCESS_KEY_ID=your_production_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_production_aws_secret_access_key
AWS_REGION=your_production_aws_region
AWS_BUCKET_NAME=your_production_aws_bucket_name

# إعدادات البريد الإلكتروني (اختياري)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_EMAIL=<EMAIL>
SMTP_PASSWORD=your_production_email_password
FROM_EMAIL=<EMAIL>
FROM_NAME=Fulk App
