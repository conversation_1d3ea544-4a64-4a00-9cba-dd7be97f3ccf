import 'package:flutter_test/flutter_test.dart';
import 'package:fulk/models/x_feed_models.dart';
import 'package:fulk/config/x_feed_config.dart';

void main() {
  group('X Feed Models Tests', () {
    test('XPostUser should create from JSON correctly', () {
      final json = {
        '_id': '123',
        'name': 'أحمد محمد',
        'avatarUrl': 'https://example.com/avatar.jpg',
      };

      final user = XPostUser.fromJson(json);

      expect(user.id, equals('123'));
      expect(user.name, equals('أحمد محمد'));
      expect(user.avatarUrl, equals('https://example.com/avatar.jpg'));
      expect(user.handle, equals('@أحمد_محمد'));
    });

    test('XPostComment should create from JSON correctly', () {
      final json = {
        '_id': 'comment123',
        'username': 'سارة أحمد',
        'content': 'تعليق رائع!',
        'date': '2024-01-15T10:30:00Z',
      };

      final comment = XPostComment.fromJson(json);

      expect(comment.id, equals('comment123'));
      expect(comment.username, equals('سارة أحمد'));
      expect(comment.content, equals('تعليق رائع!'));
      expect(comment.date, isA<DateTime>());
    });

    test('XPost should create from JSON correctly', () {
      final json = {
        '_id': 'post123',
        'userId': 'user123',
        'content': 'هذا منشور تجريبي #flutter #dart',
        'likes': 10,
        'loves': 5,
        'haha': 2,
        'comments': [],
        'createdAt': '2024-01-15T10:30:00Z',
        'promoted': false,
        'privacy': 'public',
        'hashtags': ['flutter', 'dart'],
      };

      final post = XPost.fromJson(json);

      expect(post.id, equals('post123'));
      expect(post.userId, equals('user123'));
      expect(post.content, equals('هذا منشور تجريبي #flutter #dart'));
      expect(post.likes, equals(10));
      expect(post.loves, equals(5));
      expect(post.haha, equals(2));
      expect(post.promoted, equals(false));
      expect(post.privacy, equals('public'));
      expect(post.hashtags, contains('flutter'));
      expect(post.hashtags, contains('dart'));
    });

    test('XPost should calculate total interactions correctly', () {
      final post = XPost(
        id: '1',
        userId: 'user1',
        content: 'Test post',
        likes: 10,
        loves: 5,
        haha: 3,
        comments: [
          XPostComment(
            id: '1',
            username: 'user1',
            content: 'comment1',
            date: DateTime.now(),
          ),
          XPostComment(
            id: '2',
            username: 'user2',
            content: 'comment2',
            date: DateTime.now(),
          ),
        ],
        createdAt: DateTime.now(),
        promoted: false,
        privacy: 'public',
        hashtags: [],
      );

      expect(post.totalInteractions, equals(20)); // 10+5+3+2
    });

    test('XPost should format time correctly', () {
      final now = DateTime.now();
      final post = XPost(
        id: '1',
        userId: 'user1',
        content: 'Test post',
        likes: 0,
        loves: 0,
        haha: 0,
        comments: [],
        createdAt: now.subtract(const Duration(minutes: 30)),
        promoted: false,
        privacy: 'public',
        hashtags: [],
      );

      expect(post.time, equals('منذ 30 دقيقة'));
    });

    test('XPost copyWith should work correctly', () {
      final originalPost = XPost(
        id: '1',
        userId: 'user1',
        content: 'Test post',
        likes: 10,
        loves: 5,
        haha: 3,
        comments: [],
        createdAt: DateTime.now(),
        promoted: false,
        privacy: 'public',
        hashtags: [],
        isLiked: false,
      );

      final updatedPost = originalPost.copyWith(
        likes: 11,
        isLiked: true,
      );

      expect(updatedPost.likes, equals(11));
      expect(updatedPost.isLiked, equals(true));
      expect(updatedPost.loves, equals(5)); // unchanged
      expect(updatedPost.content, equals('Test post')); // unchanged
    });
  });

  group('X Feed Config Tests', () {
    test('should validate post content correctly', () {
      expect(XFeedConfig.isValidPostContent(''), equals(false));
      expect(XFeedConfig.isValidPostContent('   '), equals(false));
      expect(XFeedConfig.isValidPostContent('Valid post'), equals(true));
      
      final longContent = 'a' * (XFeedConfig.maxPostLength + 1);
      expect(XFeedConfig.isValidPostContent(longContent), equals(false));
    });

    test('should validate comment content correctly', () {
      expect(XFeedConfig.isValidCommentContent(''), equals(false));
      expect(XFeedConfig.isValidCommentContent('   '), equals(false));
      expect(XFeedConfig.isValidCommentContent('Valid comment'), equals(true));
      
      final longComment = 'a' * (XFeedConfig.maxCommentLength + 1);
      expect(XFeedConfig.isValidCommentContent(longComment), equals(false));
    });

    test('should validate file types correctly', () {
      expect(XFeedConfig.isValidImageType('image/jpeg'), equals(true));
      expect(XFeedConfig.isValidImageType('image/png'), equals(true));
      expect(XFeedConfig.isValidImageType('image/gif'), equals(true));
      expect(XFeedConfig.isValidImageType('text/plain'), equals(false));
      
      expect(XFeedConfig.isValidVideoType('video/mp4'), equals(true));
      expect(XFeedConfig.isValidVideoType('video/mov'), equals(true));
      expect(XFeedConfig.isValidVideoType('audio/mp3'), equals(false));
    });

    test('should extract hashtags correctly', () {
      final content = 'This is a #test post with #flutter and #dart hashtags';
      final hashtags = XFeedConfig.extractHashtags(content);
      
      expect(hashtags, contains('test'));
      expect(hashtags, contains('flutter'));
      expect(hashtags, contains('dart'));
      expect(hashtags.length, equals(3));
    });

    test('should extract mentions correctly', () {
      final content = 'Hello @john and @jane, how are you?';
      final mentions = XFeedConfig.extractMentions(content);
      
      expect(mentions, contains('john'));
      expect(mentions, contains('jane'));
      expect(mentions.length, equals(2));
    });

    test('should format file size correctly', () {
      expect(XFeedConfig.formatFileSize(500), equals('500 B'));
      expect(XFeedConfig.formatFileSize(1536), equals('1.5 KB'));
      expect(XFeedConfig.formatFileSize(1572864), equals('1.5 MB'));
      expect(XFeedConfig.formatFileSize(1610612736), equals('1.5 GB'));
    });

    test('should format interaction count correctly', () {
      expect(XFeedConfig.formatInteractionCount(500), equals('500'));
      expect(XFeedConfig.formatInteractionCount(1500), equals('1.5K'));
      expect(XFeedConfig.formatInteractionCount(1500000), equals('1.5M'));
    });

    test('should detect error types correctly', () {
      expect(XFeedConfig.isNetworkError('Network connection failed'), equals(true));
      expect(XFeedConfig.isNetworkError('Connection timeout'), equals(true));
      expect(XFeedConfig.isNetworkError('Host unreachable'), equals(true));
      expect(XFeedConfig.isNetworkError('Invalid data'), equals(false));
      
      expect(XFeedConfig.isAuthError('401 Unauthorized'), equals(true));
      expect(XFeedConfig.isAuthError('Authentication failed'), equals(true));
      expect(XFeedConfig.isAuthError('Invalid token'), equals(true));
      expect(XFeedConfig.isAuthError('Network error'), equals(false));
      
      expect(XFeedConfig.isServerError('500 Internal Server Error'), equals(true));
      expect(XFeedConfig.isServerError('502 Bad Gateway'), equals(true));
      expect(XFeedConfig.isServerError('503 Service Unavailable'), equals(true));
      expect(XFeedConfig.isServerError('Network error'), equals(false));
    });

    test('should get appropriate error messages', () {
      expect(XFeedConfig.getErrorMessage('network_error'), 
             equals('خطأ في الاتصال بالشبكة'));
      expect(XFeedConfig.getErrorMessage('server_error'), 
             equals('خطأ في الخادم'));
      expect(XFeedConfig.getErrorMessage('unknown_error'), 
             equals('خطأ غير معروف'));
      expect(XFeedConfig.getErrorMessage('unknown_error', 'Custom message'), 
             equals('Custom message'));
    });
  });

  group('PostsResponse Tests', () {
    test('should create from JSON correctly', () {
      final json = {
        'success': true,
        'count': 2,
        'posts': [
          {
            '_id': 'post1',
            'userId': 'user1',
            'content': 'Post 1',
            'likes': 5,
            'loves': 2,
            'haha': 1,
            'comments': [],
            'createdAt': '2024-01-15T10:30:00Z',
            'promoted': false,
            'privacy': 'public',
            'hashtags': [],
          },
          {
            '_id': 'post2',
            'userId': 'user2',
            'content': 'Post 2',
            'likes': 3,
            'loves': 1,
            'haha': 0,
            'comments': [],
            'createdAt': '2024-01-15T11:30:00Z',
            'promoted': true,
            'privacy': 'public',
            'hashtags': ['test'],
          },
        ],
        'pagination': {
          'page': 1,
          'limit': 10,
          'total': 2,
        },
      };

      final response = PostsResponse.fromJson(json);

      expect(response.success, equals(true));
      expect(response.count, equals(2));
      expect(response.posts.length, equals(2));
      expect(response.posts[0].id, equals('post1'));
      expect(response.posts[1].id, equals('post2'));
      expect(response.pagination, isNotNull);
    });
  });
}
