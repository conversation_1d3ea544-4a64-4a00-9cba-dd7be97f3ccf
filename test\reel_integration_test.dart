import 'package:flutter_test/flutter_test.dart';
import 'package:fulk/models/reel_models.dart';
import 'package:fulk/config/reel_config.dart';
import 'package:fulk/providers/reel_provider.dart';

void main() {
  group('Reel Models Tests', () {
    test('<PERSON>elUser should create from JSON correctly', () {
      final json = {
        '_id': '123',
        'username': 'أحمد محمد',
        'avatarUrl': 'https://example.com/avatar.jpg',
        'followersCount': 1000,
        'followingCount': 500,
        'isVerified': true,
        'isFollowing': false,
      };

      final user = ReelUser.fromJson(json);

      expect(user.id, equals('123'));
      expect(user.username, equals('أحمد محمد'));
      expect(user.avatarUrl, equals('https://example.com/avatar.jpg'));
      expect(user.followersCount, equals(1000));
      expect(user.followingCount, equals(500));
      expect(user.isVerified, equals(true));
      expect(user.isFollowing, equals(false));
    });

    test('ReelComment should create from JSO<PERSON> correctly', () {
      final json = {
        '_id': 'comment123',
        'userId': 'user123',
        'username': 'سارة أحمد',
        'content': 'فيديو رائع ومفيد!',
        'createdAt': '2024-01-15T10:30:00Z',
        'likes': 5,
        'isLiked': false,
        'replies': [],
      };

      final comment = ReelComment.fromJson(json);

      expect(comment.id, equals('comment123'));
      expect(comment.userId, equals('user123'));
      expect(comment.username, equals('سارة أحمد'));
      expect(comment.content, equals('فيديو رائع ومفيد!'));
      expect(comment.likes, equals(5));
      expect(comment.isLiked, equals(false));
      expect(comment.replies, isEmpty);
    });

    test('ReelQuiz should create from JSON correctly', () {
      final json = {
        '_id': 'quiz123',
        'question': 'ما هي لغة البرمجة المستخدمة في هذا الفيديو؟',
        'options': ['Python', 'Java', 'JavaScript', 'C++'],
        'correctAnswer': 'Python',
        'explanation': 'Python هي لغة برمجة سهلة التعلم ومتعددة الاستخدامات',
        'points': 10,
        'difficulty': 'beginner',
      };

      final quiz = ReelQuiz.fromJson(json);

      expect(quiz.id, equals('quiz123'));
      expect(quiz.question, equals('ما هي لغة البرمجة المستخدمة في هذا الفيديو؟'));
      expect(quiz.options, hasLength(4));
      expect(quiz.options, contains('Python'));
      expect(quiz.correctAnswer, equals('Python'));
      expect(quiz.explanation, equals('Python هي لغة برمجة سهلة التعلم ومتعددة الاستخدامات'));
      expect(quiz.points, equals(10));
      expect(quiz.difficulty, equals('beginner'));
    });

    test('ReelVideo should create from JSON correctly', () {
      final json = {
        '_id': 'reel123',
        'userId': 'user123',
        'title': 'تعلم البرمجة بـ Python',
        'description': 'فيديو تعليمي شامل لتعلم أساسيات البرمجة',
        'category': 'programming',
        'tags': ['python', 'programming', 'tutorial'],
        'videoUrls': {
          '360p': 'https://example.com/video_360p.mp4',
          '720p': 'https://example.com/video_720p.mp4',
          '1080p': 'https://example.com/video_1080p.mp4',
        },
        'thumbnailUrl': 'https://example.com/thumbnail.jpg',
        'duration': 300, // 5 minutes
        'createdAt': '2024-01-15T10:30:00Z',
        'likes': 150,
        'dislikes': 5,
        'views': 1000,
        'shares': 25,
        'commentsCount': 30,
        'rating': 4.5,
        'isLiked': false,
        'isDisliked': false,
        'isSaved': false,
        'isFollowing': false,
        'hasWatched': false,
        'learningObjectives': ['فهم أساسيات Python', 'كتابة برامج بسيطة'],
        'difficulty': 'beginner',
        'estimatedWatchTime': 300,
        'privacy': 'public',
        'allowComments': true,
        'allowDownload': false,
        'isPromoted': false,
      };

      final reel = ReelVideo.fromJson(json);

      expect(reel.id, equals('reel123'));
      expect(reel.userId, equals('user123'));
      expect(reel.title, equals('تعلم البرمجة بـ Python'));
      expect(reel.description, equals('فيديو تعليمي شامل لتعلم أساسيات البرمجة'));
      expect(reel.category, equals('programming'));
      expect(reel.tags, hasLength(3));
      expect(reel.tags, contains('python'));
      expect(reel.videoUrls, hasLength(3));
      expect(reel.videoUrls['720p'], equals('https://example.com/video_720p.mp4'));
      expect(reel.duration.inSeconds, equals(300));
      expect(reel.likes, equals(150));
      expect(reel.views, equals(1000));
      expect(reel.rating, equals(4.5));
      expect(reel.learningObjectives, hasLength(2));
      expect(reel.difficulty, equals('beginner'));
      expect(reel.privacy, equals('public'));
      expect(reel.allowComments, equals(true));
    });

    test('ReelVideo should format duration correctly', () {
      final reel = ReelVideo(
        id: '1',
        userId: 'user1',
        title: 'Test',
        description: 'Test',
        category: 'test',
        tags: [],
        videoUrls: {},
        duration: const Duration(minutes: 5, seconds: 30),
        createdAt: DateTime.now(),
        likes: 0,
        dislikes: 0,
        views: 0,
        shares: 0,
        commentsCount: 0,
        rating: 0.0,
        isLiked: false,
        isDisliked: false,
        isSaved: false,
        isFollowing: false,
        hasWatched: false,
        learningObjectives: [],
        difficulty: 'beginner',
        estimatedWatchTime: 330,
        privacy: 'public',
        allowComments: true,
        allowDownload: false,
        isPromoted: false,
      );

      expect(reel.formattedDuration, equals('05:30'));
    });

    test('ReelVideo should format created date correctly', () {
      final now = DateTime.now();
      final reel = ReelVideo(
        id: '1',
        userId: 'user1',
        title: 'Test',
        description: 'Test',
        category: 'test',
        tags: [],
        videoUrls: {},
        duration: const Duration(minutes: 5),
        createdAt: now.subtract(const Duration(hours: 2)),
        likes: 0,
        dislikes: 0,
        views: 0,
        shares: 0,
        commentsCount: 0,
        rating: 0.0,
        isLiked: false,
        isDisliked: false,
        isSaved: false,
        isFollowing: false,
        hasWatched: false,
        learningObjectives: [],
        difficulty: 'beginner',
        estimatedWatchTime: 300,
        privacy: 'public',
        allowComments: true,
        allowDownload: false,
        isPromoted: false,
      );

      expect(reel.formattedCreatedAt, equals('منذ 2 ساعة'));
    });

    test('ReelVideo copyWith should work correctly', () {
      final originalReel = ReelVideo(
        id: '1',
        userId: 'user1',
        title: 'Test',
        description: 'Test',
        category: 'test',
        tags: [],
        videoUrls: {},
        duration: const Duration(minutes: 5),
        createdAt: DateTime.now(),
        likes: 10,
        dislikes: 2,
        views: 100,
        shares: 5,
        commentsCount: 3,
        rating: 4.0,
        isLiked: false,
        isDisliked: false,
        isSaved: false,
        isFollowing: false,
        hasWatched: false,
        learningObjectives: [],
        difficulty: 'beginner',
        estimatedWatchTime: 300,
        privacy: 'public',
        allowComments: true,
        allowDownload: false,
        isPromoted: false,
      );

      final updatedReel = originalReel.copyWith(
        likes: 11,
        isLiked: true,
        views: 101,
      );

      expect(updatedReel.likes, equals(11));
      expect(updatedReel.isLiked, equals(true));
      expect(updatedReel.views, equals(101));
      expect(updatedReel.dislikes, equals(2)); // unchanged
      expect(updatedReel.title, equals('Test')); // unchanged
    });
  });

  group('Reel Config Tests', () {
    test('should validate video size correctly', () {
      expect(ReelConfig.isValidVideoSize(50 * 1024 * 1024), equals(true)); // 50MB
      expect(ReelConfig.isValidVideoSize(150 * 1024 * 1024), equals(false)); // 150MB
    });

    test('should validate video duration correctly', () {
      expect(ReelConfig.isValidVideoDuration(30), equals(true)); // 30 seconds
      expect(ReelConfig.isValidVideoDuration(10), equals(false)); // too short
      expect(ReelConfig.isValidVideoDuration(400), equals(false)); // too long
    });

    test('should validate video types correctly', () {
      expect(ReelConfig.isValidVideoType('video/mp4'), equals(true));
      expect(ReelConfig.isValidVideoType('video/mov'), equals(true));
      expect(ReelConfig.isValidVideoType('audio/mp3'), equals(false));
      expect(ReelConfig.isValidVideoType('image/jpeg'), equals(false));
    });

    test('should validate categories correctly', () {
      expect(ReelConfig.isValidCategory('programming'), equals(true));
      expect(ReelConfig.isValidCategory('mathematics'), equals(true));
      expect(ReelConfig.isValidCategory('invalid_category'), equals(false));
    });

    test('should validate difficulty levels correctly', () {
      expect(ReelConfig.isValidDifficulty('beginner'), equals(true));
      expect(ReelConfig.isValidDifficulty('expert'), equals(true));
      expect(ReelConfig.isValidDifficulty('invalid_level'), equals(false));
    });

    test('should format duration correctly', () {
      expect(ReelConfig.formatDuration(const Duration(minutes: 5, seconds: 30)), 
             equals('05:30'));
      expect(ReelConfig.formatDuration(const Duration(minutes: 0, seconds: 45)), 
             equals('00:45'));
      expect(ReelConfig.formatDuration(const Duration(minutes: 12, seconds: 5)), 
             equals('12:05'));
    });

    test('should format file size correctly', () {
      expect(ReelConfig.formatFileSize(500), equals('500 B'));
      expect(ReelConfig.formatFileSize(1536), equals('1.5 KB'));
      expect(ReelConfig.formatFileSize(1572864), equals('1.5 MB'));
      expect(ReelConfig.formatFileSize(1610612736), equals('1.5 GB'));
    });

    test('should format view count correctly', () {
      expect(ReelConfig.formatViewCount(500), equals('500'));
      expect(ReelConfig.formatViewCount(1500), equals('1.5K'));
      expect(ReelConfig.formatViewCount(1500000), equals('1.5M'));
    });

    test('should get category names correctly', () {
      expect(ReelConfig.getCategoryName('programming'), equals('البرمجة'));
      expect(ReelConfig.getCategoryName('mathematics'), equals('الرياضيات'));
      expect(ReelConfig.getCategoryName('invalid'), equals('غير محدد'));
    });

    test('should calculate quiz points correctly', () {
      expect(ReelConfig.calculateQuizPoints('beginner', true), equals(5));
      expect(ReelConfig.calculateQuizPoints('intermediate', true), equals(10));
      expect(ReelConfig.calculateQuizPoints('advanced', true), equals(15));
      expect(ReelConfig.calculateQuizPoints('expert', true), equals(20));
      expect(ReelConfig.calculateQuizPoints('beginner', false), equals(0));
    });

    test('should determine optimal quality correctly', () {
      expect(ReelConfig.getOptimalQuality(6.0), equals('1080p'));
      expect(ReelConfig.getOptimalQuality(3.0), equals('720p'));
      expect(ReelConfig.getOptimalQuality(1.5), equals('480p'));
      expect(ReelConfig.getOptimalQuality(0.5), equals('360p'));
    });

    test('should determine auto-play correctly', () {
      expect(ReelConfig.shouldAutoPlay(true, false), equals(true)); // WiFi, no data saver
      expect(ReelConfig.shouldAutoPlay(false, false), equals(false)); // Mobile data
      expect(ReelConfig.shouldAutoPlay(true, true), equals(false)); // WiFi but data saver on
    });
  });

  group('ReelsResponse Tests', () {
    test('should create from JSON correctly', () {
      final json = {
        'success': true,
        'count': 2,
        'reels': [
          {
            '_id': 'reel1',
            'userId': 'user1',
            'title': 'Reel 1',
            'description': 'Description 1',
            'category': 'programming',
            'tags': ['test'],
            'videoUrls': {'720p': 'https://example.com/video1.mp4'},
            'duration': 300,
            'createdAt': '2024-01-15T10:30:00Z',
            'likes': 10,
            'dislikes': 1,
            'views': 100,
            'shares': 5,
            'commentsCount': 3,
            'rating': 4.0,
            'isLiked': false,
            'isDisliked': false,
            'isSaved': false,
            'isFollowing': false,
            'hasWatched': false,
            'learningObjectives': ['Learn basics'],
            'difficulty': 'beginner',
            'estimatedWatchTime': 300,
            'privacy': 'public',
            'allowComments': true,
            'allowDownload': false,
            'isPromoted': false,
          },
          {
            '_id': 'reel2',
            'userId': 'user2',
            'title': 'Reel 2',
            'description': 'Description 2',
            'category': 'mathematics',
            'tags': ['math'],
            'videoUrls': {'720p': 'https://example.com/video2.mp4'},
            'duration': 240,
            'createdAt': '2024-01-15T11:30:00Z',
            'likes': 20,
            'dislikes': 0,
            'views': 200,
            'shares': 10,
            'commentsCount': 5,
            'rating': 4.5,
            'isLiked': true,
            'isDisliked': false,
            'isSaved': true,
            'isFollowing': true,
            'hasWatched': true,
            'learningObjectives': ['Learn advanced math'],
            'difficulty': 'advanced',
            'estimatedWatchTime': 240,
            'privacy': 'public',
            'allowComments': true,
            'allowDownload': true,
            'isPromoted': true,
          },
        ],
        'pagination': {
          'page': 1,
          'limit': 10,
          'total': 2,
        },
      };

      final response = ReelsResponse.fromJson(json);

      expect(response.success, equals(true));
      expect(response.count, equals(2));
      expect(response.reels.length, equals(2));
      expect(response.reels[0].id, equals('reel1'));
      expect(response.reels[0].title, equals('Reel 1'));
      expect(response.reels[1].id, equals('reel2'));
      expect(response.reels[1].title, equals('Reel 2'));
      expect(response.pagination, isNotNull);
    });
  });
}
