import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة التخزين الآمن للبيانات الحساسة
class SecureStorageService {
  static const String _keyPrefix = 'secure_';
  static const String _rememberMeKey = '${_keyPrefix}remember_me';
  static const String _userCredentialsKey = '${_keyPrefix}user_credentials';
  static const String _encryptionKey = 'your_app_secret_key_2024'; // يجب تغييرها في الإنتاج

  /// تشفير النص باستخدام مفتاح بسيط
  static String _encrypt(String text) {
    final bytes = utf8.encode(text + _encryptionKey);
    final digest = sha256.convert(bytes);
    final encrypted = base64.encode(utf8.encode(text));
    return '$encrypted.${digest.toString().substring(0, 16)}';
  }

  /// فك تشفير النص
  static String? _decrypt(String encryptedText) {
    try {
      final parts = encryptedText.split('.');
      if (parts.length != 2) return null;
      
      final encrypted = parts[0];
      final hash = parts[1];
      
      final decrypted = utf8.decode(base64.decode(encrypted));
      
      // التحقق من صحة البيانات
      final bytes = utf8.encode(decrypted + _encryptionKey);
      final digest = sha256.convert(bytes);
      
      if (digest.toString().substring(0, 16) == hash) {
        return decrypted;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// حفظ بيانات "تذكرني"
  static Future<bool> saveRememberMeData({
    required String phoneNumber,
    required String password,
    required bool rememberMe,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      if (rememberMe) {
        final credentials = {
          'phoneNumber': phoneNumber,
          'password': password,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        };
        
        final encryptedData = _encrypt(jsonEncode(credentials));
        await prefs.setString(_userCredentialsKey, encryptedData);
        await prefs.setBool(_rememberMeKey, true);
      } else {
        await clearRememberMeData();
      }
      
      return true;
    } catch (e) {
      print('Error saving remember me data: $e');
      return false;
    }
  }

  /// استرداد بيانات "تذكرني"
  static Future<Map<String, String>?> getRememberMeData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final rememberMe = prefs.getBool(_rememberMeKey) ?? false;
      
      if (!rememberMe) return null;
      
      final encryptedData = prefs.getString(_userCredentialsKey);
      if (encryptedData == null) return null;
      
      final decryptedData = _decrypt(encryptedData);
      if (decryptedData == null) return null;
      
      final credentials = jsonDecode(decryptedData) as Map<String, dynamic>;
      final timestamp = credentials['timestamp'] as int;
      
      // التحقق من انتهاء صلاحية البيانات (30 يوم)
      final now = DateTime.now().millisecondsSinceEpoch;
      final thirtyDaysInMs = 30 * 24 * 60 * 60 * 1000;
      
      if (now - timestamp > thirtyDaysInMs) {
        await clearRememberMeData();
        return null;
      }
      
      return {
        'phoneNumber': credentials['phoneNumber'] as String,
        'password': credentials['password'] as String,
      };
    } catch (e) {
      print('Error getting remember me data: $e');
      await clearRememberMeData();
      return null;
    }
  }

  /// التحقق من وجود بيانات "تذكرني"
  static Future<bool> hasRememberMeData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_rememberMeKey) ?? false;
    } catch (e) {
      return false;
    }
  }

  /// مسح بيانات "تذكرني"
  static Future<void> clearRememberMeData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_rememberMeKey);
      await prefs.remove(_userCredentialsKey);
    } catch (e) {
      print('Error clearing remember me data: $e');
    }
  }

  /// حفظ رمز التحقق مؤقتاً
  static Future<void> saveVerificationCode({
    required String phoneNumber,
    required String code,
    required int expiryTime,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = {
        'code': code,
        'expiryTime': expiryTime,
        'phoneNumber': phoneNumber,
      };
      
      final encryptedData = _encrypt(jsonEncode(data));
      await prefs.setString('${_keyPrefix}verification_$phoneNumber', encryptedData);
    } catch (e) {
      print('Error saving verification code: $e');
    }
  }

  /// استرداد رمز التحقق
  static Future<Map<String, dynamic>?> getVerificationCode(String phoneNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final encryptedData = prefs.getString('${_keyPrefix}verification_$phoneNumber');
      
      if (encryptedData == null) return null;
      
      final decryptedData = _decrypt(encryptedData);
      if (decryptedData == null) return null;
      
      final data = jsonDecode(decryptedData) as Map<String, dynamic>;
      
      // التحقق من انتهاء الصلاحية
      final expiryTime = data['expiryTime'] as int;
      if (DateTime.now().millisecondsSinceEpoch > expiryTime) {
        await prefs.remove('${_keyPrefix}verification_$phoneNumber');
        return null;
      }
      
      return data;
    } catch (e) {
      print('Error getting verification code: $e');
      return null;
    }
  }

  /// مسح رمز التحقق
  static Future<void> clearVerificationCode(String phoneNumber) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('${_keyPrefix}verification_$phoneNumber');
    } catch (e) {
      print('Error clearing verification code: $e');
    }
  }

  /// حفظ إعدادات المستخدم
  static Future<void> saveUserSettings(Map<String, dynamic> settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final encryptedData = _encrypt(jsonEncode(settings));
      await prefs.setString('${_keyPrefix}user_settings', encryptedData);
    } catch (e) {
      print('Error saving user settings: $e');
    }
  }

  /// استرداد إعدادات المستخدم
  static Future<Map<String, dynamic>?> getUserSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final encryptedData = prefs.getString('${_keyPrefix}user_settings');
      
      if (encryptedData == null) return null;
      
      final decryptedData = _decrypt(encryptedData);
      if (decryptedData == null) return null;
      
      return jsonDecode(decryptedData) as Map<String, dynamic>;
    } catch (e) {
      print('Error getting user settings: $e');
      return null;
    }
  }

  /// مسح جميع البيانات الآمنة
  static Future<void> clearAllSecureData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith(_keyPrefix));
      
      for (final key in keys) {
        await prefs.remove(key);
      }
    } catch (e) {
      print('Error clearing all secure data: $e');
    }
  }
}
