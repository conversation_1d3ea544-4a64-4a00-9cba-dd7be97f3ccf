const asyncHandler = require('../middleware/asyncmiddleware');
const ErrorResponse = require('../utils/errorResponse');
const twilio = require('twilio');
const axios = require('axios');
const crypto = require('crypto');

// تهيئة عميل Twilio
const accountSid = process.env.TWILIO_ACCOUNT_SID || '**********************************';
const authToken = process.env.TWILIO_AUTH_TOKEN || '09237e2c808ca3ea14e555f308953cfc';
const client = twilio(accountSid, authToken);

// إعدادات الواتساب
const WHATSAPP_API_URL = process.env.WHATSAPP_API_URL || 'https://graph.facebook.com/v18.0';
const WHATSAPP_ACCESS_TOKEN = process.env.WHATSAPP_ACCESS_TOKEN;
const WHATSAPP_PHONE_NUMBER_ID = process.env.WHATSAPP_PHONE_NUMBER_ID;

// تخزين مؤقت لرموز التحقق (في الإنتاج يجب استخدام Redis)
const verificationCodes = new Map();

// توليد رمز تحقق عشوائي
const generateVerificationCode = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// إرسال رسالة عبر الواتساب
const sendWhatsAppMessage = async (phoneNumber, message) => {
  try {
    const response = await axios.post(
      `${WHATSAPP_API_URL}/${WHATSAPP_PHONE_NUMBER_ID}/messages`,
      {
        messaging_product: 'whatsapp',
        to: phoneNumber,
        type: 'text',
        text: {
          body: message
        }
      },
      {
        headers: {
          'Authorization': `Bearer ${WHATSAPP_ACCESS_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  } catch (error) {
    console.error('WhatsApp API Error:', error.response?.data || error.message);
    throw error;
  }
};

// @desc    إرسال رمز التحقق
// @route   POST /api/auth/send-verification
// @access  Public
exports.sendVerification = asyncHandler(async (req, res, next) => {
  const { phoneNumber, channel = 'whatsapp' } = req.body;

  // التحقق من وجود رقم الهاتف
  if (!phoneNumber) {
    return next(new ErrorResponse('يرجى إدخال رقم الهاتف', 400));
  }

  // تنسيق رقم الهاتف
  let formattedPhoneNumber = phoneNumber.replace(/\D/g, '');
  if (!formattedPhoneNumber.startsWith('2') && formattedPhoneNumber.length === 11) {
    formattedPhoneNumber = '2' + formattedPhoneNumber;
  }
  if (!formattedPhoneNumber.startsWith('+')) {
    formattedPhoneNumber = '+' + formattedPhoneNumber;
  }

  try {
    const verificationCode = generateVerificationCode();
    const expiryTime = Date.now() + 10 * 60 * 1000; // 10 دقائق

    // حفظ رمز التحقق مؤقتاً
    verificationCodes.set(formattedPhoneNumber, {
      code: verificationCode,
      expiryTime: expiryTime,
      attempts: 0
    });

    if (channel === 'whatsapp' && WHATSAPP_ACCESS_TOKEN) {
      // إرسال عبر الواتساب
      const message = `رمز التحقق الخاص بك هو: ${verificationCode}\n\nهذا الرمز صالح لمدة 10 دقائق فقط.\n\nلا تشارك هذا الرمز مع أي شخص آخر.`;

      await sendWhatsAppMessage(formattedPhoneNumber, message);

      res.status(200).json({
        success: true,
        message: 'تم إرسال رمز التحقق عبر الواتساب بنجاح',
        data: {
          phoneNumber: formattedPhoneNumber,
          channel: 'whatsapp',
          expiryTime: expiryTime
        }
      });
    } else {
      // الرجوع إلى Twilio SMS كبديل
      const verification = await client.verify.v2
        .services(process.env.TWILIO_VERIFY_SERVICE_SID)
        .verifications.create({
          to: formattedPhoneNumber,
          channel: 'sms'
        });

      res.status(200).json({
        success: true,
        message: 'تم إرسال رمز التحقق عبر الرسائل النصية',
        data: {
          status: verification.status,
          to: verification.to,
          channel: 'sms'
        }
      });
    }
  } catch (error) {
    console.error('Verification Error:', error);
    return next(new ErrorResponse(`فشل إرسال رمز التحقق: ${error.message}`, 500));
  }
});

// @desc    التحقق من الرمز
// @route   POST /api/auth/verify-code
// @access  Public
exports.verifyCode = asyncHandler(async (req, res, next) => {
  const { phoneNumber, code } = req.body;

  // التحقق من وجود رقم الهاتف والرمز
  if (!phoneNumber || !code) {
    return next(new ErrorResponse('يرجى إدخال رقم الهاتف ورمز التحقق', 400));
  }

  // تنسيق رقم الهاتف
  let formattedPhoneNumber = phoneNumber.replace(/\D/g, '');
  if (!formattedPhoneNumber.startsWith('2') && formattedPhoneNumber.length === 11) {
    formattedPhoneNumber = '2' + formattedPhoneNumber;
  }
  if (!formattedPhoneNumber.startsWith('+')) {
    formattedPhoneNumber = '+' + formattedPhoneNumber;
  }

  try {
    const storedData = verificationCodes.get(formattedPhoneNumber);

    if (!storedData) {
      return next(new ErrorResponse('لم يتم العثور على رمز التحقق. يرجى طلب رمز جديد', 400));
    }

    // التحقق من انتهاء صلاحية الرمز
    if (Date.now() > storedData.expiryTime) {
      verificationCodes.delete(formattedPhoneNumber);
      return next(new ErrorResponse('انتهت صلاحية رمز التحقق. يرجى طلب رمز جديد', 400));
    }

    // التحقق من عدد المحاولات
    if (storedData.attempts >= 3) {
      verificationCodes.delete(formattedPhoneNumber);
      return next(new ErrorResponse('تم تجاوز عدد المحاولات المسموح. يرجى طلب رمز جديد', 400));
    }

    // التحقق من صحة الرمز
    if (storedData.code === code) {
      // حذف الرمز بعد التحقق الناجح
      verificationCodes.delete(formattedPhoneNumber);

      res.status(200).json({
        success: true,
        message: 'تم التحقق من الرمز بنجاح',
        data: {
          phoneNumber: formattedPhoneNumber,
          verified: true
        }
      });
    } else {
      // زيادة عدد المحاولات
      storedData.attempts += 1;
      verificationCodes.set(formattedPhoneNumber, storedData);

      const remainingAttempts = 3 - storedData.attempts;
      return next(new ErrorResponse(`رمز التحقق غير صحيح. المحاولات المتبقية: ${remainingAttempts}`, 400));
    }
  } catch (error) {
    console.error('Verification Error:', error);
    return next(new ErrorResponse(`فشل التحقق من الرمز: ${error.message}`, 500));
  }
});

// @desc    إرسال رمز استرداد كلمة المرور
// @route   POST /api/auth/forgot-password
// @access  Public
exports.forgotPassword = asyncHandler(async (req, res, next) => {
  const { phoneNumber } = req.body;

  if (!phoneNumber) {
    return next(new ErrorResponse('يرجى إدخال رقم الهاتف', 400));
  }

  // تنسيق رقم الهاتف
  let formattedPhoneNumber = phoneNumber.replace(/\D/g, '');
  if (!formattedPhoneNumber.startsWith('2') && formattedPhoneNumber.length === 11) {
    formattedPhoneNumber = '2' + formattedPhoneNumber;
  }
  if (!formattedPhoneNumber.startsWith('+')) {
    formattedPhoneNumber = '+' + formattedPhoneNumber;
  }

  try {
    // التحقق من وجود المستخدم
    const User = require('../models/user.model');
    const user = await User.findOne({ phoneNumber: formattedPhoneNumber });

    if (!user) {
      return next(new ErrorResponse('لم يتم العثور على حساب مرتبط بهذا الرقم', 404));
    }

    const resetCode = generateVerificationCode();
    const expiryTime = Date.now() + 15 * 60 * 1000; // 15 دقيقة

    // حفظ رمز الاسترداد
    verificationCodes.set(`reset_${formattedPhoneNumber}`, {
      code: resetCode,
      expiryTime: expiryTime,
      attempts: 0,
      userId: user._id
    });

    // إرسال رمز الاسترداد عبر الواتساب
    if (WHATSAPP_ACCESS_TOKEN) {
      const message = `رمز استرداد كلمة المرور: ${resetCode}\n\nهذا الرمز صالح لمدة 15 دقيقة فقط.\n\nإذا لم تطلب استرداد كلمة المرور، يرجى تجاهل هذه الرسالة.`;

      await sendWhatsAppMessage(formattedPhoneNumber, message);
    }

    res.status(200).json({
      success: true,
      message: 'تم إرسال رمز استرداد كلمة المرور عبر الواتساب',
      data: {
        phoneNumber: formattedPhoneNumber,
        expiryTime: expiryTime
      }
    });
  } catch (error) {
    console.error('Forgot Password Error:', error);
    return next(new ErrorResponse(`فشل إرسال رمز الاسترداد: ${error.message}`, 500));
  }
});

// @desc    إعادة تعيين كلمة المرور
// @route   POST /api/auth/reset-password
// @access  Public
exports.resetPassword = asyncHandler(async (req, res, next) => {
  const { phoneNumber, code, newPassword } = req.body;

  if (!phoneNumber || !code || !newPassword) {
    return next(new ErrorResponse('يرجى إدخال جميع البيانات المطلوبة', 400));
  }

  // تنسيق رقم الهاتف
  let formattedPhoneNumber = phoneNumber.replace(/\D/g, '');
  if (!formattedPhoneNumber.startsWith('2') && formattedPhoneNumber.length === 11) {
    formattedPhoneNumber = '2' + formattedPhoneNumber;
  }
  if (!formattedPhoneNumber.startsWith('+')) {
    formattedPhoneNumber = '+' + formattedPhoneNumber;
  }

  try {
    const storedData = verificationCodes.get(`reset_${formattedPhoneNumber}`);

    if (!storedData) {
      return next(new ErrorResponse('رمز الاسترداد غير صالح أو منتهي الصلاحية', 400));
    }

    // التحقق من انتهاء صلاحية الرمز
    if (Date.now() > storedData.expiryTime) {
      verificationCodes.delete(`reset_${formattedPhoneNumber}`);
      return next(new ErrorResponse('انتهت صلاحية رمز الاسترداد', 400));
    }

    // التحقق من صحة الرمز
    if (storedData.code !== code) {
      return next(new ErrorResponse('رمز الاسترداد غير صحيح', 400));
    }

    // تحديث كلمة المرور
    const User = require('../models/user.model');
    const user = await User.findById(storedData.userId);

    if (!user) {
      return next(new ErrorResponse('المستخدم غير موجود', 404));
    }

    user.password = newPassword;
    await user.save();

    // حذف رمز الاسترداد
    verificationCodes.delete(`reset_${formattedPhoneNumber}`);

    res.status(200).json({
      success: true,
      message: 'تم تغيير كلمة المرور بنجاح'
    });
  } catch (error) {
    console.error('Reset Password Error:', error);
    return next(new ErrorResponse(`فشل إعادة تعيين كلمة المرور: ${error.message}`, 500));
  }
});
