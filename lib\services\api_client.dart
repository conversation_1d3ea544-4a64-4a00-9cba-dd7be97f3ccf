import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:fulk/config/env_config.dart';
import 'package:fulk/utils/api_error_handler.dart';

/// عميل API لإجراء طلبات الشبكة
/// يستخدم Dio للاتصال بالخادم مع معالجة الأخطاء والتوثيق
class ApiClient {
  late final Dio _dio;
  final EnvConfig _envConfig = EnvConfig();
  
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط
  static final ApiClient _instance = ApiClient._internal();
  factory ApiClient() => _instance;
  
  ApiClient._internal() {
    _initDio();
  }

  /// تهيئة Dio مع الإعدادات الأساسية
  void _initDio() {
    final baseOptions = BaseOptions(
      baseUrl: _envConfig.apiUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );

    _dio = Dio(baseOptions);
    
    // إضافة معترضات للتسجيل والتوثيق
    _dio.interceptors.add(LogInterceptor(
      requestBody: kDebugMode,
      responseBody: kDebugMode,
      logPrint: (object) {
        if (kDebugMode) {
          debugPrint(object.toString());
        }
      },
    ));

    // إضافة معترض للتوثيق
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        // إضافة رمز التوثيق إذا كان متاحًا
        final token = _getAuthToken();
        if (token != null && token.isNotEmpty) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        return handler.next(options);
      },
      onError: (DioException error, handler) {
        // معالجة أخطاء التوثيق
        if (error.response?.statusCode == 401) {
          // يمكن إضافة منطق لتحديث الرمز أو تسجيل الخروج
        }
        return handler.next(error);
      },
    ));
  }

  /// الحصول على رمز التوثيق من التخزين المحلي
  String? _getAuthToken() {
    // يمكن استبدال هذا بمنطق الحصول على الرمز من التخزين المحلي
    // مثال: return await secureStorage.read(key: 'auth_token');
    return null; // استبدل هذا بالتنفيذ الفعلي
  }

  /// تعيين رمز التوثيق
  void setAuthToken(String token) {
    _dio.options.headers['Authorization'] = 'Bearer $token';
    // يمكن إضافة منطق لحفظ الرمز في التخزين المحلي
  }

  /// إجراء طلب GET
  Future<dynamic> get(String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      final response = await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
      return _processResponse(response);
    } catch (e) {
      throw ApiError.fromException(e);
    }
  }

  /// إجراء طلب POST
  Future<dynamic> post(String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      final response = await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      return _processResponse(response);
    } catch (e) {
      throw ApiError.fromException(e);
    }
  }

  /// إجراء طلب PUT
  Future<dynamic> put(String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      final response = await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      return _processResponse(response);
    } catch (e) {
      throw ApiError.fromException(e);
    }
  }

  /// إجراء طلب DELETE
  Future<dynamic> delete(String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      return _processResponse(response);
    } catch (e) {
      throw ApiError.fromException(e);
    }
  }

  /// إجراء طلب PATCH
  Future<dynamic> patch(String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      final response = await _dio.patch(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      return _processResponse(response);
    } catch (e) {
      throw ApiError.fromException(e);
    }
  }

  /// رفع ملف
  Future<dynamic> uploadFile(
    String path, {
    required File file,
    required String fieldName,
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      final fileName = file.path.split('/').last;
      final formData = FormData.fromMap({
        ...?data,
        fieldName: await MultipartFile.fromFile(
          file.path,
          filename: fileName,
        ),
      });

      final response = await _dio.post(
        path,
        data: formData,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      return _processResponse(response);
    } catch (e) {
      throw ApiError.fromException(e);
    }
  }

  /// معالجة الاستجابة وتحويلها إلى البيانات المناسبة
  dynamic _processResponse(Response response) {
    if (response.statusCode == 204) {
      return null; // لا محتوى
    }

    if (response.data == null) {
      return null;
    }

    return response.data;
  }

  /// إعادة تهيئة العميل (مفيد عند تغيير الإعدادات)
  void reset() {
    _initDio();
  }
}
