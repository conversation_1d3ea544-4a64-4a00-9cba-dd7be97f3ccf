import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:animate_do/animate_do.dart';
import 'package:image_picker/image_picker.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:io';
import '../appstate.dart';
import '../models/user_model.dart';
import '../services/whatsapp_auth_service.dart';
import '../services/secure_storage_service.dart';

class EnhancedRegistrationPage extends StatefulWidget {
  final String? verifiedPhoneNumber;

  const EnhancedRegistrationPage({
    super.key,
    this.verifiedPhoneNumber,
  });

  @override
  State<EnhancedRegistrationPage> createState() =>
      _EnhancedRegistrationPageState();
}

class _EnhancedRegistrationPageState extends State<EnhancedRegistrationPage>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _verificationCodeController = TextEditingController();

  bool _isLoading = false;
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _isPhoneVerified = false;
  bool _isVerificationStep = false;
  String _verificationPhoneNumber = '';

  String userType = UserRole.member.name;
  String? job;
  XFile? idCardImage;
  String? location;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late AnimationController _stepAnimationController;
  late Animation<Offset> _slideAnimation;

  final List<String> jobs = [
    'مهندس',
    'مدرس',
    'موظف حكومي',
    'محاسب',
    'مبرمج',
    'سائق',
    'عامل حر (فريلانسر)',
    'طالب',
    'صاحب عمل/تاجر',
    'أخرى',
  ];

  final List<String> medicalJobs = [
    'مستشفى',
    'مركز أشعة',
    'معمل تحاليل',
    'عيادة خاصة',
    'صيدلية',
    'مركز علاج طبيعي',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();

    // Initialize animations
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _stepAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _stepAnimationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();

    // Set verified phone number if provided
    if (widget.verifiedPhoneNumber != null) {
      _phoneController.text = WhatsAppAuthService.getDisplayPhoneNumber(
          widget.verifiedPhoneNumber!);
      _isPhoneVerified = true;
    }

    _checkLocationPermission();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _stepAnimationController.dispose();
    _usernameController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _verificationCodeController.dispose();
    super.dispose();
  }

  Future<void> _checkLocationPermission() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
      }
      if (permission == LocationPermission.whileInUse ||
          permission == LocationPermission.always) {
        Position position = await Geolocator.getCurrentPosition();
        setState(() {
          location = '${position.latitude},${position.longitude}';
        });
      }
    } catch (e) {
      print('Error getting location: $e');
    }
  }

  Future<void> _pickIdCardImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(
      source: ImageSource.gallery,
      maxWidth: 1024,
      maxHeight: 1024,
      imageQuality: 85,
    );
    if (pickedFile != null) {
      setState(() {
        idCardImage = pickedFile;
      });
    }
  }

  void _showSnackBar(String message, [Color? backgroundColor]) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(fontFamily: 'Tajawal'),
        ),
        backgroundColor: backgroundColor ?? Colors.teal[700],
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  Future<void> _sendVerificationCode() async {
    if (!WhatsAppAuthService.isValidEgyptianPhoneNumber(
        _phoneController.text)) {
      _showSnackBar('يرجى إدخال رقم هاتف مصري صحيح', Colors.red);
      return;
    }

    setState(() => _isLoading = true);

    try {
      final result = await WhatsAppAuthService.sendVerificationCode(
        phoneNumber: _phoneController.text,
        channel: 'whatsapp',
      );

      if (result['success']) {
        setState(() {
          _isVerificationStep = true;
          _verificationPhoneNumber = result['phoneNumber'];
        });
        _stepAnimationController.forward();
        _showSnackBar(result['message'], Colors.green);
      } else {
        _showSnackBar(result['message'], Colors.red);
      }
    } catch (e) {
      _showSnackBar('حدث خطأ في إرسال رمز التحقق', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _verifyCode() async {
    if (_verificationCodeController.text.length != 6) {
      _showSnackBar('يرجى إدخال رمز التحقق المكون من 6 أرقام', Colors.red);
      return;
    }

    setState(() => _isLoading = true);

    try {
      final result = await WhatsAppAuthService.verifyCode(
        phoneNumber: _verificationPhoneNumber,
        code: _verificationCodeController.text,
      );

      if (result['success']) {
        setState(() {
          _isVerificationStep = false;
          _isPhoneVerified = true;
        });
        _stepAnimationController.reverse();
        _showSnackBar('تم التحقق بنجاح!', Colors.green);
      } else {
        _showSnackBar(result['message'], Colors.red);
      }
    } catch (e) {
      _showSnackBar('حدث خطأ في التحقق من الرمز', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _register() async {
    if (!_formKey.currentState!.validate()) return;

    if (!_isPhoneVerified) {
      _showSnackBar('يرجى التحقق من رقم الهاتف أولاً', Colors.red);
      return;
    }

    if (_passwordController.text != _confirmPasswordController.text) {
      _showSnackBar('كلمات المرور غير متطابقة', Colors.red);
      return;
    }

    setState(() => _isLoading = true);

    try {
      final appState = Provider.of<AppState>(context, listen: false);
      await appState.register(
        _usernameController.text,
        _passwordController.text,
        _phoneController.text,
        job ?? 'أخرى',
        idCardImage?.path,
        location,
        userType,
      );

      if (appState.isLoggedIn) {
        _showSnackBar('تم إنشاء الحساب بنجاح', Colors.green);
        Navigator.of(context).pushReplacementNamed('/');
      }
    } catch (e) {
      _showSnackBar('فشل في إنشاء الحساب: $e', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _goBack() {
    if (_isVerificationStep) {
      setState(() {
        _isVerificationStep = false;
      });
      _stepAnimationController.reverse();
    } else {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.teal[700]!, Colors.teal[200]!],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Stack(
              children: [
                _buildMainForm(),
                if (_isVerificationStep)
                  SlideTransition(
                    position: _slideAnimation,
                    child: _buildVerificationStep(),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMainForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            // Header
            const SizedBox(height: 20),
            ZoomIn(
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.person_add,
                      size: 60,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'إنشاء حساب جديد',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontFamily: 'Tajawal',
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'يرجى ملء البيانات التالية',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white70,
                      fontFamily: 'Tajawal',
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 40),

            // User Type Selection
            SlideInLeft(
              child: _buildNeumorphicField(
                child: DropdownButtonFormField<String>(
                  value: userType,
                  decoration: const InputDecoration(
                    labelText: 'نوع المستخدم',
                    prefixIcon:
                        Icon(Icons.person_outline, color: Colors.white70),
                    border: InputBorder.none,
                    labelStyle:
                        TextStyle(color: Colors.white70, fontFamily: 'Tajawal'),
                  ),
                  dropdownColor: Colors.teal[600],
                  style: const TextStyle(
                      color: Colors.white, fontFamily: 'Tajawal'),
                  items: [
                    UserRole.hokama,
                    UserRole.olama,
                    UserRole.mogtahdin,
                    UserRole.medical,
                    UserRole.member
                  ]
                      .map((role) => DropdownMenuItem(
                            value: role.name,
                            child: Text(
                              role.label,
                              style: const TextStyle(
                                fontFamily: 'Tajawal',
                                color: Colors.white,
                              ),
                            ),
                          ))
                      .toList(),
                  onChanged: (value) {
                    setState(() {
                      if (value != null) {
                        userType = value;
                        job = null; // Reset job when user type changes
                      }
                    });
                  },
                  validator: (value) =>
                      value == null ? 'يرجى اختيار نوع المستخدم' : null,
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Username Field
            SlideInRight(
              child: _buildNeumorphicField(
                child: TextFormField(
                  controller: _usernameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم المستخدم',
                    hintText: 'أدخل اسمك الكامل',
                    prefixIcon:
                        Icon(Icons.account_circle, color: Colors.white70),
                    border: InputBorder.none,
                    labelStyle:
                        TextStyle(color: Colors.white70, fontFamily: 'Tajawal'),
                    hintStyle:
                        TextStyle(color: Colors.white54, fontFamily: 'Tajawal'),
                  ),
                  style: const TextStyle(
                      color: Colors.white, fontFamily: 'Tajawal'),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال اسم المستخدم';
                    }
                    if (value.length < 3) {
                      return 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
                    }
                    return null;
                  },
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Phone Number Field
            SlideInLeft(
              child: _buildNeumorphicField(
                child: Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _phoneController,
                        decoration: InputDecoration(
                          labelText: 'رقم الهاتف',
                          hintText: '01xxxxxxxxx',
                          prefixIcon:
                              const Icon(Icons.phone, color: Colors.white70),
                          border: InputBorder.none,
                          labelStyle: const TextStyle(
                              color: Colors.white70, fontFamily: 'Tajawal'),
                          hintStyle: const TextStyle(
                              color: Colors.white54, fontFamily: 'Tajawal'),
                          suffixIcon: _isPhoneVerified
                              ? const Icon(Icons.verified, color: Colors.green)
                              : null,
                        ),
                        style: const TextStyle(
                            color: Colors.white, fontFamily: 'Tajawal'),
                        keyboardType: TextInputType.phone,
                        enabled: !_isPhoneVerified,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال رقم الهاتف';
                          }
                          if (!WhatsAppAuthService.isValidEgyptianPhoneNumber(
                              value)) {
                            return 'يرجى إدخال رقم هاتف مصري صحيح';
                          }
                          return null;
                        },
                      ),
                    ),
                    if (!_isPhoneVerified)
                      TextButton(
                        onPressed: _isLoading ? null : _sendVerificationCode,
                        child: _isLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              )
                            : const Text(
                                'تحقق',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontFamily: 'Tajawal',
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Password Field
            SlideInRight(
              child: _buildNeumorphicField(
                child: TextFormField(
                  controller: _passwordController,
                  decoration: InputDecoration(
                    labelText: 'كلمة المرور',
                    prefixIcon: const Icon(Icons.lock, color: Colors.white70),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _isPasswordVisible
                            ? Icons.visibility
                            : Icons.visibility_off,
                        color: Colors.white70,
                      ),
                      onPressed: () {
                        setState(() {
                          _isPasswordVisible = !_isPasswordVisible;
                        });
                      },
                    ),
                    border: InputBorder.none,
                    labelStyle: const TextStyle(
                        color: Colors.white70, fontFamily: 'Tajawal'),
                  ),
                  style: const TextStyle(
                      color: Colors.white, fontFamily: 'Tajawal'),
                  obscureText: !_isPasswordVisible,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال كلمة المرور';
                    }
                    if (value.length < 6) {
                      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                    }
                    return null;
                  },
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Confirm Password Field
            SlideInLeft(
              child: _buildNeumorphicField(
                child: TextFormField(
                  controller: _confirmPasswordController,
                  decoration: InputDecoration(
                    labelText: 'تأكيد كلمة المرور',
                    prefixIcon:
                        const Icon(Icons.lock_outline, color: Colors.white70),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _isConfirmPasswordVisible
                            ? Icons.visibility
                            : Icons.visibility_off,
                        color: Colors.white70,
                      ),
                      onPressed: () {
                        setState(() {
                          _isConfirmPasswordVisible =
                              !_isConfirmPasswordVisible;
                        });
                      },
                    ),
                    border: InputBorder.none,
                    labelStyle: const TextStyle(
                        color: Colors.white70, fontFamily: 'Tajawal'),
                  ),
                  style: const TextStyle(
                      color: Colors.white, fontFamily: 'Tajawal'),
                  obscureText: !_isConfirmPasswordVisible,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى تأكيد كلمة المرور';
                    }
                    if (value != _passwordController.text) {
                      return 'كلمات المرور غير متطابقة';
                    }
                    return null;
                  },
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Job Selection (if not guest)
            if (userType != 'guest')
              SlideInRight(
                child: _buildNeumorphicField(
                  child: DropdownButtonFormField<String>(
                    value: job,
                    decoration: const InputDecoration(
                      labelText: 'المهنة',
                      prefixIcon: Icon(Icons.work, color: Colors.white70),
                      border: InputBorder.none,
                      labelStyle: TextStyle(
                          color: Colors.white70, fontFamily: 'Tajawal'),
                    ),
                    dropdownColor: Colors.teal[600],
                    style: const TextStyle(
                        color: Colors.white, fontFamily: 'Tajawal'),
                    items: (userType == 'medical' ? medicalJobs : jobs)
                        .map((jobTitle) => DropdownMenuItem(
                              value: jobTitle,
                              child: Text(
                                jobTitle,
                                style: const TextStyle(
                                  fontFamily: 'Tajawal',
                                  color: Colors.white,
                                ),
                              ),
                            ))
                        .toList(),
                    onChanged: (value) {
                      setState(() {
                        job = value;
                      });
                    },
                    validator: (value) =>
                        value == null ? 'يرجى اختيار المهنة' : null,
                  ),
                ),
              ),
            if (userType != 'guest') const SizedBox(height: 20),

            // ID Card Upload (if not guest)
            if (userType != 'guest')
              SlideInLeft(
                child: _buildNeumorphicField(
                  child: ListTile(
                    leading:
                        const Icon(Icons.credit_card, color: Colors.white70),
                    title: Text(
                      idCardImage == null
                          ? 'رفع صورة البطاقة الشخصية'
                          : 'تم رفع الصورة',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontFamily: 'Tajawal',
                      ),
                    ),
                    trailing: idCardImage != null
                        ? const Icon(Icons.check_circle, color: Colors.green)
                        : const Icon(Icons.upload, color: Colors.white70),
                    onTap: _pickIdCardImage,
                  ),
                ),
              ),
            if (userType != 'guest') const SizedBox(height: 30),

            // Register Button
            ZoomIn(
              child: _isLoading
                  ? const CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    )
                  : _buildGradientButton(
                      text: 'إنشاء الحساب',
                      onPressed: _register,
                      icon: Icons.person_add,
                    ),
            ),
            const SizedBox(height: 20),

            // Back to Login
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'لديك حساب؟ تسجيل الدخول',
                style: TextStyle(
                  color: Colors.white70,
                  fontFamily: 'Tajawal',
                  fontSize: 16,
                ),
              ),
            ),
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Widget _buildVerificationStep() {
    return Container(
      color: Colors.teal[700]!.withOpacity(0.95),
      child: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Back Button
              Align(
                alignment: Alignment.topRight,
                child: IconButton(
                  onPressed: _goBack,
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                ),
              ),
              const SizedBox(height: 20),

              // WhatsApp Icon
              ZoomIn(
                child: Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.message,
                    size: 60,
                    color: Colors.white,
                  ),
                ),
              ),
              const SizedBox(height: 30),

              // Title
              const Text(
                'التحقق من رقم الهاتف',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontFamily: 'Tajawal',
                ),
              ),
              const SizedBox(height: 10),

              // Subtitle
              Text(
                'تم إرسال رمز التحقق إلى\n${WhatsAppAuthService.getDisplayPhoneNumber(_verificationPhoneNumber)}',
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.white70,
                  fontFamily: 'Tajawal',
                ),
              ),
              const SizedBox(height: 40),

              // Verification Code Field
              SlideInUp(
                child: _buildNeumorphicField(
                  child: TextFormField(
                    controller: _verificationCodeController,
                    decoration: const InputDecoration(
                      labelText: 'رمز التحقق',
                      hintText: '123456',
                      prefixIcon: Icon(Icons.security, color: Colors.white70),
                      border: InputBorder.none,
                      labelStyle: TextStyle(
                          color: Colors.white70, fontFamily: 'Tajawal'),
                      hintStyle: TextStyle(
                          color: Colors.white54, fontFamily: 'Tajawal'),
                    ),
                    style: const TextStyle(
                        color: Colors.white, fontFamily: 'Tajawal'),
                    keyboardType: TextInputType.number,
                    textAlign: TextAlign.center,
                    maxLength: 6,
                  ),
                ),
              ),
              const SizedBox(height: 30),

              // Verify Button
              ZoomIn(
                child: _isLoading
                    ? const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      )
                    : _buildGradientButton(
                        text: 'تأكيد الرمز',
                        onPressed: _verifyCode,
                        icon: Icons.check,
                      ),
              ),
              const SizedBox(height: 20),

              // Resend Code
              TextButton(
                onPressed: _sendVerificationCode,
                child: const Text(
                  'إعادة إرسال الرمز',
                  style: TextStyle(
                    color: Colors.white70,
                    fontFamily: 'Tajawal',
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNeumorphicField({required Widget child}) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
      child: child,
    );
  }

  Widget _buildGradientButton({
    required String text,
    required VoidCallback onPressed,
    required IconData icon,
  }) {
    return Container(
      width: double.infinity,
      height: 55,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white.withOpacity(0.9),
            Colors.white.withOpacity(0.7)
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(15),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: Colors.teal[700], size: 24),
              const SizedBox(width: 10),
              Text(
                text,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.teal[700],
                  fontFamily: 'Tajawal',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
