<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>فلك</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>com.fulkapp.fulk</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Fulk</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>

	<!-- أذونات الكاميرا والميكروفون -->
	<key>NSCameraUsageDescription</key>
	<string>يحتاج تطبيق فلك للوصول إلى الكاميرا لالتقاط الصور ومقاطع الفيديو</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>يحتاج تطبيق فلك للوصول إلى الميكروفون لتسجيل الصوت</string>

	<!-- أذونات الموقع -->
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>يحتاج تطبيق فلك للوصول إلى موقعك لتوفير خدمات محلية أفضل</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>يحتاج تطبيق فلك للوصول إلى موقعك لتوفير خدمات محلية أفضل</string>

	<!-- أذونات معرض الصور -->
	<key>NSPhotoLibraryUsageDescription</key>
	<string>يحتاج تطبيق فلك للوصول إلى معرض الصور لمشاركة الصور</string>

	<!-- إعدادات الشبكة -->
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>

	<!-- دعم الخلفية -->
	<key>UIBackgroundModes</key>
	<array>
		<string>background-fetch</string>
		<string>background-processing</string>
		<string>remote-notification</string>
	</array>
</dict>
</plist>
