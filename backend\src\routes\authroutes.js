const express = require('express');
const router = express.Router();
const {
  register,
  login,
  logout,
  getMe,
  updateDetails,
  updatePassword,
  updateLocation
} = require('../controllers/authcontroller');
const { sendVerification, verifyCode, forgotPassword, resetPassword } = require('../controllers/verificationController');
const { protect } = require('../middleware/authmiddleware');

// مسارات المصادقة
router.post('/register', register);
router.post('/login', login);
router.get('/logout', protect, logout);
router.get('/me', protect, getMe);
router.put('/updatedetails', protect, updateDetails);
router.put('/updatepassword', protect, updatePassword);
router.put('/updatelocation', protect, updateLocation);

// مسارات التحقق من رقم الهاتف باستخدام الواتساب و Twilio
router.post('/send-verification', sendVerification);
router.post('/verify-code', verifyCode);

// مسارات استرداد كلمة المرور
router.post('/forgot-password', forgotPassword);
router.post('/reset-password', resetPassword);

module.exports = router;
