import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:sqflite_common_ffi_web/sqflite_ffi_web.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart';
import 'package:fulk/widgets/custom_snackbar.dart';
import 'package:share_plus/share_plus.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:webview_flutter/webview_flutter.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart' as path;
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:fulk/main.dart' show MyApp;
import 'profession_service.dart';
import 'appstate.dart';
import 'package:fulk/models/profession_model.dart' show DemandLevel, Profession;
import 'models/notification_model.dart';
import 'widgets/loading_indicator.dart';
import 'profession_details_page.dart';
import 'repositories/profession_repository.dart';
import 'utils/role_checker.dart';

// Add this import for WebView
import 'package:webview_flutter/webview_flutter.dart' show WebView, WebViewController, WebViewWidget, JavascriptMode;
import 'package:image_picker/image_picker.dart';
import 'package:uuid/uuid.dart';

void setupTimeago() {
  timeago.setLocaleMessages('ar', timeago.ArMessages());
  timeago.setLocaleMessages('en', timeago.EnMessages());
}

class ProfessionsListPage extends StatefulWidget {
  const ProfessionsListPage({super.key});

  @override
  _ProfessionsListPageState createState() => _ProfessionsListPageState();
}

class _ProfessionsListPageState extends State<ProfessionsListPage>
    with SingleTickerProviderStateMixin {
  bool get isGoldTheme => Theme.of(context).brightness == Brightness.dark;

  final ProfessionService _professionService = ProfessionService();
  final TextEditingController _searchController = TextEditingController();
  final SpeechToText _speechToText = SpeechToText();
  List<Profession> _technicalProfessions = [];
  List<Profession> _creativeProfessions = [];
  List<Profession> _serviceProfessions = [];
  List<Profession> _healthProfessions = [];
  List<Profession> _educationProfessions = [];
  List<Profession> _socialProfessions = [];
  List<Profession> _recommendedProfessions = [];
  bool _isLoading = true;
  bool _isSpeechEnabled = false;
  Timer? _debounceTimer;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;
  final String _selectedCategory = 'all';
  final Map<String, List<Contribution>> _contributions = {};
  final Map<String, List<Subtopic>> _subtopics = {};
  final Map<String, List<FAQ>> _faqs = {};
  final Map<String, List<Event>> _events = {};
  final Map<String, List<Mentor>> _mentors = {};
  final Map<String, List<StudyGroup>> _studyGroups = {};
  Database? _database;
  final String _currentLanguage = 'ar';
  
  // Add appState getter
  AppState get appState => Provider.of<AppState>(context, listen: false);
  final List<Message> _communityMessages = [];

  final List<Map<String, dynamic>> _professionsList = [
    {
      'category': 'technical',
      'name': 'مبرمج',
      'resources': ['برمجيات مفتوحة المصدر', 'مكتبات برمجية', 'تطبيقات مجانية'],
      'subtopics': ['تطوير الويب', 'تطوير التطبيقات', 'تعلم الآلة']
    },
  ];

  final List<String> _searchSuggestions = [
    'مبرمج',
    'مصمم جرافيك',
    'طباخ',
    'نجار',
    'كهربائي',
    'مصور فوتوغرافي',
    'خبير تسويق رقمي',
    'محلل بيانات',
    'مطور تطبيقات',
    'مهندس شبكات',
    'طبيب',
    'معلم',
    'محامي',
    'مترجم',
    'أخصائي اجتماعي',
  ];

  final Map<String, Map<String, String>> _translations = {
    'ar': {
      'app_title': 'اكتشف مهنتك المستقبلية',
      'search_hint': 'ابحث عن مهنة أو دورة...',
      'contribute': 'ساهم',
      'resources': 'الموارد',
      'community': 'المنتدى',
      'profile': 'ملفي',
      'subtopics': 'المواضيع الفرعية',
      'faq': 'الأسئلة الشائعة',
      'events': 'الفعاليات وورش العمل',
      'mentors': 'المرشدون',
      'study_groups': 'مجموعات دراسية',
    },
    'en': {
      'app_title': 'Discover Your Future Career',
      'search_hint': 'Search for a profession or course...',
      'contribute': 'Contribute',
      'resources': 'Resources',
      'community': 'Forum',
      'profile': 'My Profile',
      'subtopics': 'Subtopics',
      'faq': 'FAQ',
      'events': 'Events & Workshops',
      'mentors': 'Mentors',
      'study_groups': 'Study Groups',
    },
  };

  @override
  void initState() {
    super.initState();
    setupTimeago();
    _initializeSpeech();
    _initializeDatabase();
    _fetchData();
    _searchController.addListener(_onSearchChanged);

    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _fabAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fabAnimationController,
      curve: Curves.easeOut,
    ));
    _fabAnimationController.forward();

    final appState = Provider.of<AppState>(context, listen: false);
    if (appState.socket != null) {
      appState.socket!.on('new_notification', (data) {
        if (mounted) {
          final notification = NotificationModel(
            id: data['notificationId'] ?? DateTime.now().toString(),
            message: data['message'] ??
                _getTranslation('new_notification', 'إشعار جديد'),
            date: DateTime.now().toIso8601String(),
            isRead: false,
          );
          appState.addNotification(notification);
        }
      });
      appState.socket!.on('new_contribution', (data) {
        if (mounted) {
          setState(() {
            _contributions[data['profession']] ??= [];
            _contributions[data['profession']]!.add(Contribution(
              id: data['id'],
              userId: data['userId'],
              resource: data['resource'],
              resourceType: data['resourceType'],
              timestamp: DateTime.now(),
              isApproved: false,
              downloadCount: 0,
              rating: 0.0,
              comments: [],
              likes: 0,
              mediaUrl: data['mediaUrl'],
              subtopic: data['subtopic'],
            ));
          });
          _awardPoints(data['userId'], 10);
          _saveContribution(data);
        }
      });
      appState.socket!.on('resource_downloaded', (data) {
        if (mounted) {
          setState(() {
            final contribution = _contributions[data['profession']]!
                .firstWhere((c) => c.id == data['id']);
            contribution.downloadCount++;
          });
          if (data['userId'] == appState.userId) {
            appState.addNotification(NotificationModel(
              id: DateTime.now().toString(),
              message:
              'تم تنزيل موردك ${data['resource']} ${data['downloads']} مرات!',
              date: DateTime.now().toIso8601String(),
              isRead: false,
            ));
          }
        }
      });
      appState.socket!.on('resource_rated', (data) {
        if (mounted) {
          setState(() {
            final contribution = _contributions[data['profession']]!
                .firstWhere((c) => c.id == data['id']);
            contribution.rating =
                (contribution.rating * contribution.downloadCount +
                    data['rating']) /
                    (contribution.downloadCount + 1);
            contribution.comments.add(data['comment']);
            if (data['liked'] == true) {
              contribution.likes++;
            }
          });
        }
      });
      appState.socket!.on('new_message', (data) {
        if (mounted) {
          setState(() {
            _communityMessages.add(Message(
              id: data['id'],
              userId: data['userId'],
              profession: data['profession'],
              subtopic: data['subtopic'],
              content: data['content'],
              timestamp: DateTime.now(),
              mediaUrl: data['mediaUrl'],
              likes: 0,
              comments: [],
            ));
          });
        }
      });
      appState.socket!.on('new_faq', (data) {
        if (mounted) {
          setState(() {
            _faqs[data['profession']] ??= [];
            _faqs[data['profession']]!.add(FAQ(
              id: data['id'],
              question: data['question'],
              answer: data['answer'],
              profession: data['profession'],
            ));
          });
        }
      });
      appState.socket!.on('new_event', (data) {
        if (mounted) {
          setState(() {
            _events[data['profession']] ??= [];
            _events[data['profession']]!.add(Event(
              id: data['id'],
              title: data['title'],
              description: data['description'],
              date: DateTime.parse(data['date']),
              isVirtual: data['isVirtual'],
              location: data['location'],
              profession: data['profession'],
            ));
          });
        }
      });
      appState.socket!.on('new_mentor', (data) {
        if (mounted) {
          setState(() {
            _mentors[data['profession']] ??= [];
            _mentors[data['profession']]!.add(Mentor(
              id: data['id'],
              name: data['name'],
              expertise: data['expertise'],
              contact: data['contact'],
              profession: data['profession'],
            ));
          });
        }
      });
      appState.socket!.on('new_study_group', (data) {
        if (mounted) {
          setState(() {
            _studyGroups[data['profession']] ??= [];
            _studyGroups[data['profession']]!.add(StudyGroup(
              id: data['id'],
              name: data['name'],
              description: data['description'],
              members: List<String>.from(data['members']),
              profession: data['profession'],
            ));
          });
        }
      });
    }
  }

  Future<void> _initializeDatabase() async {
    if (kIsWeb) {
      databaseFactory = databaseFactoryFfiWeb;
    } else {
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    }

    _database = await openDatabase(
      path.join(await getDatabasesPath(), 'professions.db'),
      version: 2,
      onCreate: (db, version) async {
        await db.execute('''
          CREATE TABLE contributions (
            id TEXT PRIMARY KEY,
            userId TEXT,
            profession TEXT,
            resource TEXT,
            resourceType TEXT,
            timestamp TEXT,
            isApproved INTEGER,
            downloadCount INTEGER,
            rating REAL,
            comments TEXT,
            previewUrl TEXT,
            mediaUrl TEXT,
            likes INTEGER,
            subtopic TEXT
          )
        ''');
        await db.execute('''
          CREATE TABLE subtopics (
            id TEXT PRIMARY KEY,
            profession TEXT,
            name TEXT
          )
        ''');
        await db.execute('''
          CREATE TABLE faqs (
            id TEXT PRIMARY KEY,
            profession TEXT,
            question TEXT,
            answer TEXT
          )
        ''');
        await db.execute('''
          CREATE TABLE events (
            id TEXT PRIMARY KEY,
            profession TEXT,
            title TEXT,
            description TEXT,
            date TEXT,
            isVirtual INTEGER,
            location TEXT
          )
        ''');
        await db.execute('''
          CREATE TABLE mentors (
            id TEXT PRIMARY KEY,
            profession TEXT,
            name TEXT,
            expertise TEXT,
            contact TEXT
          )
        ''');
        await db.execute('''
          CREATE TABLE study_groups (
            id TEXT PRIMARY KEY,
            profession TEXT,
            name TEXT,
            description TEXT,
            members TEXT
          )
        ''');
      },
      onUpgrade: (db, oldVersion, newVersion) async {
        if (oldVersion < 2) {
          await db.execute('ALTER TABLE contributions ADD COLUMN mediaUrl TEXT');
          await db.execute('ALTER TABLE contributions ADD COLUMN likes INTEGER DEFAULT 0');
          await db.execute('ALTER TABLE contributions ADD COLUMN subtopic TEXT');
          await db.execute('''
            CREATE TABLE subtopics (
              id TEXT PRIMARY KEY,
              profession TEXT,
              name TEXT
            )
          ''');
          await db.execute('''
            CREATE TABLE faqs (
              id TEXT PRIMARY KEY,
              profession TEXT,
              question TEXT,
              answer TEXT
            )
          ''');
          await db.execute('''
            CREATE TABLE events (
              id TEXT PRIMARY KEY,
              profession TEXT,
              title TEXT,
              description TEXT,
              date TEXT,
              isVirtual INTEGER,
              location TEXT
            )
          ''');
          await db.execute('''
            CREATE TABLE mentors (
              id TEXT PRIMARY KEY,
              profession TEXT,
              name TEXT,
              expertise TEXT,
              contact TEXT
            )
          ''');
          await db.execute('''
            CREATE TABLE study_groups (
              id TEXT PRIMARY KEY,
              profession TEXT,
              name TEXT,
              description TEXT,
              members TEXT
            )
          ''');
        }
      },
    );
    await _loadContributions();
    await _loadSubtopics();
    await _loadFAQs();
    await _loadEvents();
    await _loadMentors();
    await _loadStudyGroups();
  }

  Future<void> _saveContribution(Map<String, dynamic> data) async {
    if (_database != null) {
      await _database!.insert(
          'contributions',
          {
            'id': data['id'],
            'userId': data['userId'],
            'profession': data['profession'],
            'resource': data['resource'],
            'resourceType': data['resourceType'],
            'timestamp': DateTime.now().toIso8601String(),
            'isApproved': 0,
            'downloadCount': 0,
            'rating': 0.0,
            'comments': jsonEncode([]),
            'previewUrl': data['previewUrl'],
            'mediaUrl': data['mediaUrl'],
            'likes': 0,
            'subtopic': data['subtopic'],
          },
          conflictAlgorithm: ConflictAlgorithm.replace);
    }
  }

  Future<void> _loadContributions() async {
    if (_database != null) {
      final List<Map<String, dynamic>> maps =
      await _database!.query('contributions');
      setState(() {
        _contributions.clear();
        for (var map in maps) {
          _contributions[map['profession']] ??= [];
          _contributions[map['profession']]!.add(Contribution(
            id: map['id'],
            userId: map['userId'],
            resource: map['resource'],
            resourceType: map['resourceType'],
            timestamp: DateTime.parse(map['timestamp']),
            isApproved: map['isApproved'] == 1,
            downloadCount: map['downloadCount'],
            rating: map['rating'],
            comments: List<String>.from(jsonDecode(map['comments'])),
            previewUrl: map['previewUrl'],
            mediaUrl: map['mediaUrl'],
            likes: map['likes'] ?? 0,
            subtopic: map['subtopic'],
          ));
        }
      });
    }
  }

  Future<void> _loadSubtopics() async {
    if (_database != null) {
      final List<Map<String, dynamic>> maps =
      await _database!.query('subtopics');
      setState(() {
        _subtopics.clear();
        for (var map in maps) {
          _subtopics[map['profession']] ??= [];
          _subtopics[map['profession']]!.add(Subtopic(
            id: map['id'],
            name: map['name'],
            profession: map['profession'],
          ));
        }
      });
    }
  }

  Future<void> _loadFAQs() async {
    if (_database != null) {
      final List<Map<String, dynamic>> maps = await _database!.query('faqs');
      setState(() {
        _faqs.clear();
        for (var map in maps) {
          _faqs[map['profession']] ??= [];
          _faqs[map['profession']]!.add(FAQ(
            id: map['id'],
            question: map['question'],
            answer: map['answer'],
            profession: map['profession'],
          ));
        }
      });
    }
  }

  Future<void> _loadEvents() async {
    if (_database != null) {
      final List<Map<String, dynamic>> maps = await _database!.query('events');
      setState(() {
        _events.clear();
        for (var map in maps) {
          _events[map['profession']] ??= [];
          _events[map['profession']]!.add(Event(
            id: map['id'],
            title: map['title'],
            description: map['description'],
            date: DateTime.parse(map['date']),
            isVirtual: map['isVirtual'] == 1,
            location: map['location'],
            profession: map['profession'],
          ));
        }
      });
    }
  }

  Future<void> _loadMentors() async {
    if (_database != null) {
      final List<Map<String, dynamic>> maps = await _database!.query('mentors');
      setState(() {
        _mentors.clear();
        for (var map in maps) {
          _mentors[map['profession']] ??= [];
          _mentors[map['profession']]!.add(Mentor(
            id: map['id'],
            name: map['name'],
            expertise: map['expertise'],
            contact: map['contact'],
            profession: map['profession'],
          ));
        }
      });
    }
  }

  Future<void> _loadStudyGroups() async {
    if (_database != null) {
      final List<Map<String, dynamic>> maps =
      await _database!.query('study_groups');
      setState(() {
        _studyGroups.clear();
        for (var map in maps) {
          _studyGroups[map['profession']] ??= [];
          _studyGroups[map['profession']]!.add(StudyGroup(
            id: map['id'],
            name: map['name'],
            description: map['description'],
            members: List<String>.from(jsonDecode(map['members'])),
            profession: map['profession'],
          ));
        }
      });
    }
  }

  void _awardPoints(String userId, int points) {
    final appState = Provider.of<AppState>(context, listen: false);
    appState.addUserPoints(userId, points);
    final userPoints = appState.getUserPoints(userId);
    if (userPoints >= 100) {
      appState.awardBadge(
          userId, _getTranslation('distinguished_badge', 'متميز'));
      _showSnackBar(_getTranslation(
          'badge_earned', 'تهانينا! لقد حصلت على شارة "متميز"!'));
    }
    final contributions = _contributions.values
        .expand((c) => c)
        .where((c) => c.userId == userId)
        .length;
    if (contributions >= 5) {
      appState.awardBadge(
          userId, _getTranslation('contributor_badge', 'مساهم نشط'));
    }
  }

  Future<bool> _validateLink(String url) async {
    try {
      final response = await http.head(Uri.parse(url));
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  Future<String?> _uploadMedia(XFile file) async {
    try {
      return 'https://example.com/uploads/${file.name}';
    } catch (e) {
      _showSnackBar('خطأ في رفع الوسائط: $e', Colors.red);
      return null;
    }
  }

  Future<void> _initializeSpeech() async {
    _isSpeechEnabled = await _speechToText.initialize();
    setState(() {});
  }

  Future<void> _fetchData() async {
    if (!mounted) return;
    setState(() => _isLoading = true);

    final professionRepo = ProfessionRepository();
    bool hasErrors = false;

    try {
      final futures = [
        professionRepo.getProfessionsByType('technical').then((response) {
          if (response.isSuccess && response.data != null && mounted) {
            setState(() {
              _technicalProfessions = response.data!;
            });
          } else {
            hasErrors = true;
          }
        }).catchError((e) {
          hasErrors = true;
        }),
      ];

      await Future.wait(futures, eagerError: false);

      if (hasErrors && mounted) {
        _showSnackBar(
            _getTranslation('offline_data',
                'تم استخدام البيانات المحلية بسبب مشاكل في الاتصال'),
            Colors.orange);
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar(
            _getTranslation(
                'error_data', 'استخدام البيانات المحلية بسبب خطأ: $e'),
            Colors.orange);
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _onSearchChanged() {
    _debounce(() {
      if (mounted) {
        setState(() {});
      }
    });
  }

  void _debounce(VoidCallback callback) {
    const duration = Duration(milliseconds: 300);
    _debounceTimer?.cancel();
    _debounceTimer = Timer(duration, callback);
  }

  void _startListening() async {
    if (_isSpeechEnabled) {
      await _speechToText.listen(
        onResult: (result) {
          setState(() {
            _searchController.text = result.recognizedWords;
          });
        },
        localeId: 'ar_SA',
      );
    } else {
      _showSnackBar('التعرف على الصوت غير متوفر', Colors.red);
    }
  }

  void _stopListening() {
    _speechToText.stop();
    setState(() {});
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debounceTimer?.cancel();
    _fabAnimationController.dispose();
    _speechToText.stop();
    _database?.close();
    super.dispose();
  }

  void _showSnackBar(String message, [Color? backgroundColor, IconData? icon]) {
    CustomSnackbar.show(
      context,
      message: message,
      backgroundColor: backgroundColor ?? Theme.of(context).primaryColor,
      icon: icon,
    );
  }

  String _getTranslation(String key, String defaultValue) {
    try {
      final appState = Provider.of<AppState>(context, listen: false);
      return appState.translations[appState.currentLanguage]?[key] ?? defaultValue;
    } catch (e) {
      return defaultValue;
    }
  }

  void _showAddOptionsDialog(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    if (appState.userRole != 'hokama') {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('عذراً، هذه الميزة متاحة فقط للحكماء'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.85,
        ),
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: const EdgeInsets.all(8),
              width: 50,
              height: 5,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.5),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.work),
              title: const Text('إضافة مهنة جديدة'),
              onTap: () {
                Navigator.pop(context);
                _showAddProfessionDialog(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.school),
              title: const Text('إضافة دورة تدريبية'),
              onTap: () {
                Navigator.pop(context);
                _showAddCourseDialog(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.question_answer),
              title: const Text('إضافة سؤال شائع'),
              onTap: () {
                Navigator.pop(context);
                _showAddFAQDialog(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.event),
              title: const Text('إضافة فعالية/ورشة عمل'),
              onTap: () {
                Navigator.pop(context);
                _showAddEventDialog(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.person_add),
              title: const Text('إضافة مرشد'),
              onTap: () {
                Navigator.pop(context);
                _showAddMentorDialog(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.group),
              title: const Text('إضافة مجموعة دراسية'),
              onTap: () {
                Navigator.pop(context);
                _showAddStudyGroupDialog(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showOptionsDialog(BuildContext context, bool isGoldTheme) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة محتوى جديد'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.work,
                  color: isGoldTheme ? Colors.amber[700] : Colors.blue),
              title: const Text('إضافة مهنة جديدة'),
              onTap: () {
                Navigator.pop(context);
                _showAddProfessionDialog(context);
              },
            ),
            ListTile(
              leading: Icon(Icons.school,
                  color: isGoldTheme ? Colors.amber[700] : Colors.blue),
              title: const Text('إضافة دورة تدريبية'),
              onTap: () {
                Navigator.pop(context);
                _showAddCourseDialog(context);
              },
            ),
            ListTile(
              leading: Icon(Icons.question_answer,
                  color: isGoldTheme ? Colors.amber[700] : Colors.blue),
              title: const Text('إضافة سؤال شائع'),
              onTap: () {
                Navigator.pop(context);
                _showAddFAQDialog(context);
              },
            ),
            ListTile(
              leading: Icon(Icons.event,
                  color: isGoldTheme ? Colors.amber[700] : Colors.blue),
              title: const Text('إضافة فعالية/ورشة عمل'),
              onTap: () {
                Navigator.pop(context);
                _showAddEventDialog(context);
              },
            ),
            ListTile(
              leading: Icon(Icons.person_add,
                  color: isGoldTheme ? Colors.amber[700] : Colors.blue),
              title: const Text('إضافة مرشد'),
              onTap: () {
                Navigator.pop(context);
                _showAddMentorDialog(context);
              },
            ),
            ListTile(
              leading: Icon(Icons.group,
                  color: isGoldTheme ? Colors.amber[700] : Colors.blue),
              title: const Text('إضافة مجموعة دراسية'),
              onTap: () {
                Navigator.pop(context);
                _showAddStudyGroupDialog(context);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showAddCourseDialog(BuildContext context) {
    RoleChecker.checkPermissionAndExecute(
      context,
      Provider.of<AppState>(context, listen: false),
          () {
        final TextEditingController titleController = TextEditingController();
        final TextEditingController descriptionController =
        TextEditingController();
        final TextEditingController categoryController =
        TextEditingController();
        final TextEditingController urlController = TextEditingController();
        bool isLoading = false;
        showDialog(
          context: context,
          builder: (context) => StatefulBuilder(
            builder: (context, setState) => AlertDialog(
              title: const Text('إضافة دورة تدريبية'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: titleController,
                      decoration: const InputDecoration(
                        labelText: 'اسم الدورة *',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 10),
                    TextField(
                      controller: categoryController,
                      decoration: const InputDecoration(
                        labelText: 'الفئة',
                        border: OutlineInputBorder(),
                        hintText: 'مثال: تقنية، برمجة، تطوير الذات ...',
                      ),
                    ),
                    const SizedBox(height: 10),
                    TextField(
                      controller: descriptionController,
                      maxLines: 3,
                      decoration: const InputDecoration(
                        labelText: 'وصف الدورة',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 10),
                    TextField(
                      controller: urlController,
                      decoration: const InputDecoration(
                        labelText: 'رابط الدورة (اختياري)',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    if (isLoading) ...[
                      const SizedBox(height: 16),
                      const CircularProgressIndicator(),
                    ]
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: isLoading
                      ? null
                      : () async {
                    if (titleController.text.isEmpty) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('يرجى إدخال اسم الدورة'),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }
                    setState(() => isLoading = true);
                    try {
                      final response =
                      await _professionService.apiService.post(
                        '/api/courses',
                        body: {
                          'title': titleController.text,
                          'description': descriptionController.text,
                          'category': categoryController.text,
                          'url': urlController.text,
                        },
                        parser: (data) => data,
                      );
                      if (response.isSuccess) {
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('تمت إضافة الدورة بنجاح'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      } else {
                        throw Exception(response.message ?? 'حدث خطأ');
                      }
                    } catch (e) {
                      setState(() => isLoading = false);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('خطأ في إضافة الدورة: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  },
                  child: const Text('إضافة'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showAddProfessionDialog(BuildContext context) {
    RoleChecker.checkPermissionAndExecute(
      context,
      Provider.of<AppState>(context, listen: false),
          () {
        final TextEditingController nameController = TextEditingController();
        final TextEditingController descriptionController =
        TextEditingController();
        final TextEditingController categoryController =
        TextEditingController();
        final TextEditingController requirementsController =
        TextEditingController();
        final TextEditingController salaryRangeController =
        TextEditingController();
        final TextEditingController subtopicsController =
        TextEditingController();

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('إضافة مهنة جديدة'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'اسم المهنة *',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 10),
                  TextField(
                    controller: categoryController,
                    decoration: const InputDecoration(
                      labelText: 'الفئة *',
                      border: OutlineInputBorder(),
                      hintText:
                      'تقنية، إبداعية، خدمية، صحية، تعليمية، اجتماعية',
                    ),
                  ),
                  const SizedBox(height: 10),
                  TextField(
                    controller: descriptionController,
                    maxLines: 3,
                    decoration: const InputDecoration(
                      labelText: 'وصف المهنة',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 10),
                  TextField(
                    controller: requirementsController,
                    maxLines: 3,
                    decoration: const InputDecoration(
                      labelText: 'المهارات المطلوبة',
                      border: OutlineInputBorder(),
                      hintText: 'أدخل كل مهارة في سطر منفصل',
                    ),
                  ),
                  const SizedBox(height: 10),
                  TextField(
                    controller: salaryRangeController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'متوسط الراتب',
                      border: OutlineInputBorder(),
                      hintText: 'مثال: 5000',
                    ),
                  ),
                  const SizedBox(height: 10),
                  TextField(
                    controller: subtopicsController,
                    maxLines: 3,
                    decoration: const InputDecoration(
                      labelText: 'المواضيع الفرعية',
                      border: OutlineInputBorder(),
                      hintText: 'أدخل كل موضوع فرعي في سطر منفصل',
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () async {
                  if (nameController.text.isEmpty ||
                      categoryController.text.isEmpty) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('يرجى ملء الحقول المطلوبة'),
                        backgroundColor: Colors.red,
                      ),
                    );
                    return;
                  }
                  try {
                    final response = await _professionService.apiService.post(
                      '/api/professions',
                      body: {
                        'title': nameController.text,
                        'category': categoryController.text,
                        'description': descriptionController.text,
                        'averageSalary': salaryRangeController.text,
                        'requiredSkills': requirementsController.text
                            .split('\n')
                            .where((s) => s.trim().isNotEmpty)
                            .toList(),
                        'subtopics': subtopicsController.text
                            .split('\n')
                            .where((s) => s.trim().isNotEmpty)
                            .toList(),
                      },
                      parser: (data) => data,
                    );
                    if (response.isSuccess) {
                      final newProfession = Profession(
                        id: response.data['id']?.toString() ??
                            DateTime.now().millisecondsSinceEpoch.toString(),
                        title: nameController.text,
                        description: descriptionController.text,
                        image:
                        'https://example.com/default-profession-image.png',
                        averageSalary:
                        int.tryParse(salaryRangeController.text) ?? 0,
                        demand: DemandLevel.medium,
                        requiredSkills: requirementsController.text
                            .split('\n')
                            .where((s) => s.trim().isNotEmpty)
                            .toList(),
                      );
                      setState(() {
                        final category = categoryController.text.toLowerCase();
                        switch (category) {
                          case 'تقنية':
                          case 'technical':
                            _technicalProfessions.add(newProfession);
                            break;
                          case 'إبداعية':
                          case 'creative':
                            _creativeProfessions.add(newProfession);
                            break;
                          case 'خدمية':
                          case 'service':
                            _serviceProfessions.add(newProfession);
                            break;
                          case 'صحية':
                          case 'health':
                            _healthProfessions.add(newProfession);
                            break;
                          case 'تعليمية':
                          case 'education':
                            _educationProfessions.add(newProfession);
                            break;
                          default:
                            _technicalProfessions.add(newProfession);
                        }
                        final subtopics = subtopicsController.text
                            .split('\n')
                            .where((s) => s.trim().isNotEmpty)
                            .toList();
                        _subtopics[nameController.text] = subtopics
                            .map((s) => Subtopic(
                          id: Uuid().v4(),
                          name: s,
                          profession: nameController.text,
                        ))
                            .toList();
                      });
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('تمت إضافة المهنة بنجاح'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    } else {
                      throw Exception(response.message ?? 'حدث خطأ');
                    }
                  } catch (e) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('خطأ في إضافة المهنة: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                },
                child: const Text('إضافة'),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showAddFAQDialog(BuildContext context) {
    RoleChecker.checkPermissionAndExecute(
      context,
      Provider.of<AppState>(context, listen: false),
          () {
        final TextEditingController questionController = TextEditingController();
        final TextEditingController answerController = TextEditingController();
        final TextEditingController professionController = TextEditingController();
        bool isLoading = false;

        showDialog(
          context: context,
          builder: (context) => StatefulBuilder(
            builder: (context, setState) => AlertDialog(
              title: const Text('إضافة سؤال شائع'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: professionController,
                      decoration: const InputDecoration(
                        labelText: 'المهنة *',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 10),
                    TextField(
                      controller: questionController,
                      decoration: const InputDecoration(
                        labelText: 'السؤال *',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 10),
                    TextField(
                      controller: answerController,
                      maxLines: 3,
                      decoration: const InputDecoration(
                        labelText: 'الإجابة *',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    if (isLoading) ...[
                      const SizedBox(height: 16),
                      const CircularProgressIndicator(),
                    ]
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: isLoading
                      ? null
                      : () async {
                    if (questionController.text.isEmpty ||
                        answerController.text.isEmpty ||
                        professionController.text.isEmpty) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('يرجى ملء الحقول المطلوبة'),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }
                    setState(() => isLoading = true);
                    try {
                      final response =
                      await _professionService.apiService.post(
                        '/api/faqs',
                        body: {
                          'question': questionController.text,
                          'answer': answerController.text,
                          'profession': professionController.text,
                        },
                        parser: (data) => data,
                      );
                      if (response.isSuccess) {
                        final faq = FAQ(
                          id: response.data['id']?.toString() ??
                              Uuid().v4(),
                          question: questionController.text,
                          answer: answerController.text,
                          profession: professionController.text,
                        );
                        setState(() {
                          _faqs[professionController.text] ??= [];
                          _faqs[professionController.text]!.add(faq);
                        });
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('تمت إضافة السؤال بنجاح'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      } else {
                        throw Exception(response.message ?? 'حدث خطأ');
                      }
                    } catch (e) {
                      setState(() => isLoading = false);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('خطأ في إضافة السؤال: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  },
                  child: const Text('إضافة'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showAddEventDialog(BuildContext context) {
    RoleChecker.checkPermissionAndExecute(
      context,
      Provider.of<AppState>(context, listen: false),
          () {
        final TextEditingController titleController = TextEditingController();
        final TextEditingController descriptionController =
        TextEditingController();
        final TextEditingController professionController =
        TextEditingController();
        final TextEditingController locationController = TextEditingController();
        bool isVirtual = false;
        DateTime? selectedDate;
        bool isLoading = false;

        showDialog(
          context: context,
          builder: (context) => StatefulBuilder(
            builder: (context, setState) => AlertDialog(
              title: const Text('إضافة فعالية/ورشة عمل'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: professionController,
                      decoration: const InputDecoration(
                        labelText: 'المهنة *',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 10),
                    TextField(
                      controller: titleController,
                      decoration: const InputDecoration(
                        labelText: 'عنوان الفعالية *',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 10),
                    TextField(
                      controller: descriptionController,
                      maxLines: 3,
                      decoration: const InputDecoration(
                        labelText: 'وصف الفعالية',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 10),
                    TextField(
                      controller: locationController,
                      decoration: const InputDecoration(
                        labelText: 'الموقع (إن لم يكن افتراضي)',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 10),
                    CheckboxListTile(
                      title: const Text('فعالية افتراضية'),
                      value: isVirtual,
                      onChanged: (value) {
                        setState(() => isVirtual = value!);
                      },
                    ),
                    const SizedBox(height: 10),
                    ElevatedButton(
                      onPressed: () async {
                        final DateTime? picked = await showDatePicker(
                          context: context,
                          initialDate: DateTime.now(),
                          firstDate: DateTime.now(),
                          lastDate: DateTime.now().add(Duration(days: 365)),
                        );
                        if (picked != null) {
                          setState(() => selectedDate = picked);
                        }
                      },
                      child: Text(
                        selectedDate == null
                            ? 'اختر التاريخ'
                            : DateFormat.yMMMd().format(selectedDate!),
                      ),
                    ),
                    if (isLoading) ...[
                      const SizedBox(height: 16),
                      const CircularProgressIndicator(),
                    ]
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: isLoading
                      ? null
                      : () async {
                    if (titleController.text.isEmpty ||
                        professionController.text.isEmpty ||
                        selectedDate == null) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('يرجى ملء الحقول المطلوبة'),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }
                    setState(() => isLoading = true);
                    try {
                      final response =
                      await _professionService.apiService.post(
                        '/api/events',
                        body: {
                          'title': titleController.text,
                          'description': descriptionController.text,
                          'profession': professionController.text,
                          'date': selectedDate!.toIso8601String(),
                          'isVirtual': isVirtual,
                          'location': locationController.text,
                        },
                        parser: (data) => data,
                      );
                      if (response.isSuccess) {
                        final event = Event(
                          id: response.data['id']?.toString() ??
                              Uuid().v4(),
                          title: titleController.text,
                          description: descriptionController.text,
                          date: selectedDate!,
                          isVirtual: isVirtual,
                          location: locationController.text,
                          profession: professionController.text,
                        );
                        setState(() {
                          _events[professionController.text] ??= [];
                          _events[professionController.text]!.add(event);
                        });
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('تمت إضافة الفعالية بنجاح'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      } else {
                        throw Exception(response.message ?? 'حدث خطأ');
                      }
                    } catch (e) {
                      setState(() => isLoading = false);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('خطأ في إضافة الفعالية: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  },
                  child: const Text('إضافة'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showAddMentorDialog(BuildContext context) {
    RoleChecker.checkPermissionAndExecute(
      context,
      Provider.of<AppState>(context, listen: false),
          () {
        final TextEditingController nameController = TextEditingController();
        final TextEditingController expertiseController = TextEditingController();
        final TextEditingController contactController = TextEditingController();
        final TextEditingController professionController =
        TextEditingController();
        bool isLoading = false;

        showDialog(
          context: context,
          builder: (context) => StatefulBuilder(
            builder: (context, setState) => AlertDialog(
              title: const Text('إضافة مرشد'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: professionController,
                      decoration: const InputDecoration(
                        labelText: 'المهنة *',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 10),
                    TextField(
                      controller: nameController,
                      decoration: const InputDecoration(
                        labelText: 'اسم المرشد *',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 10),
                    TextField(
                      controller: expertiseController,
                      maxLines: 2,
                      decoration: const InputDecoration(
                        labelText: 'التخصص',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 10),
                    TextField(
                      controller: contactController,
                      decoration: const InputDecoration(
                        labelText: 'معلومات التواصل',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    if (isLoading) ...[
                      const SizedBox(height: 16),
                      const CircularProgressIndicator(),
                    ]
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: isLoading
                      ? null
                      : () async {
                    if (nameController.text.isEmpty ||
                        professionController.text.isEmpty) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('يرجى ملء الحقول المطلوبة'),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }
                    setState(() => isLoading = true);
                    try {
                      final response =
                      await _professionService.apiService.post(
                        '/api/mentors',
                        body: {
                          'name': nameController.text,
                          'expertise': expertiseController.text,
                          'contact': contactController.text,
                          'profession': professionController.text,
                        },
                        parser: (data) => data,
                      );
                      if (response.isSuccess) {
                        final mentor = Mentor(
                          id: response.data['id']?.toString() ??
                              Uuid().v4(),
                          name: nameController.text,
                          expertise: expertiseController.text,
                          contact: contactController.text,
                          profession: professionController.text,
                        );
                        setState(() {
                          _mentors[professionController.text] ??= [];
                          _mentors[professionController.text]!.add(mentor);
                        });
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('تمت إضافة المرشد بنجاح'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      } else {
                        throw Exception(response.message ?? 'حدث خطأ');
                      }
                    } catch (e) {
                      setState(() => isLoading = false);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('خطأ في إضافة المرشد: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  },
                  child: const Text('إضافة'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showAddStudyGroupDialog(BuildContext context) {
    RoleChecker.checkPermissionAndExecute(
      context,
      Provider.of<AppState>(context, listen: false),
          () {
        final TextEditingController nameController = TextEditingController();
        final TextEditingController descriptionController =
        TextEditingController();
        final TextEditingController professionController =
        TextEditingController();
        bool isLoading = false;

        showDialog(
          context: context,
          builder: (context) => StatefulBuilder(
            builder: (context, setState) => AlertDialog(
              title: const Text('إضافة مجموعة دراسية'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: professionController,
                      decoration: const InputDecoration(
                        labelText: 'المهنة *',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 10),
                    TextField(
                      controller: nameController,
                      decoration: const InputDecoration(
                        labelText: 'اسم المجموعة *',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 10),
                    TextField(
                      controller: descriptionController,
                      maxLines: 3,
                      decoration: const InputDecoration(
                        labelText: 'وصف المجموعة',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    if (isLoading) ...[
                      const SizedBox(height: 16),
                      const CircularProgressIndicator(),
                    ]
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: isLoading
                      ? null
                      : () async {
                    if (nameController.text.isEmpty ||
                        professionController.text.isEmpty) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('يرجى ملء الحقول المطلوبة'),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }
                    setState(() => isLoading = true);
                    try {
                      final appState = Provider.of<AppState>(context,
                          listen: false);
                      final response =
                      await _professionService.apiService.post(
                        '/api/study_groups',
                        body: {
                          'name': nameController.text,
                          'description': descriptionController.text,
                          'profession': professionController.text,
                          'members': [appState.userId],
                        },
                        parser: (data) => data,
                      );
                      if (response.isSuccess) {
                        final studyGroup = StudyGroup(
                          id: response.data['id']?.toString() ??
                              Uuid().v4(),
                          name: nameController.text,
                          description: descriptionController.text,
                          members: [appState.userId ?? ''],
                          profession: professionController.text,
                        );
                        setState(() {
                          _studyGroups[professionController.text] ??= [];
                          _studyGroups[professionController.text]!
                              .add(studyGroup);
                        });
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('تمت إضافة المجموعة بنجاح'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      } else {
                        throw Exception(response.message ?? 'حدث خطأ');
                      }
                    } catch (e) {
                      setState(() => isLoading = false);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('خطأ في إضافة المجموعة: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  },
                  child: const Text('إضافة'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final isGoldTheme = isDarkMode;
    final appState = Provider.of<AppState>(context);
    final ao = _technicalProfessions
        .where((p) => p.title
        .toLowerCase()
        .contains(_searchController.text.toLowerCase()))
        .toList();

    final themeKey = isDarkMode ? 'Dark' : 'Default';
    final currentTheme = MyApp.themes[themeKey]?['themeData'] as ThemeData?;

    return Theme(
      data: currentTheme?.copyWith(
        textTheme: GoogleFonts.tajawalTextTheme(
          currentTheme.textTheme,
        ),
      ) ?? Theme.of(context),
      child: Scaffold(
        body: Stack(
          children: [
            _isLoading
                ? _buildSkeletonLoader()
                : RefreshIndicator(
              onRefresh: _fetchData,
              color: isGoldTheme ? Colors.amber[700] : Colors.blue,
              child: CustomScrollView(
                slivers: [
                  _buildSliverAppBar(isDarkMode,
                      appState.notifications.length, isGoldTheme),
                  SliverToBoxAdapter(
                    child: _buildCategoryTabs(isGoldTheme),
                  ),
                  SliverList(
                    delegate: SliverChildListDelegate([
                      const SizedBox(height: 80),
                    ]),
                  ),
                ],
              ),
            ),
          ],
        ),
        floatingActionButton: RoleChecker.isHokama(appState)
            ? ScaleTransition(
          scale: _fabAnimation,
          child: FloatingActionButton(
            onPressed: () => _showOptionsDialog(context, isGoldTheme),
            backgroundColor:
            isGoldTheme ? Colors.amber[700] : Colors.blue,
            tooltip: 'إضافة محتوى جديد',
            elevation: 8,
            child: Icon(
              Icons.add,
              size: 30,
              color: isGoldTheme ? Colors.black : Colors.white,
            ),
          ),
        )
            : null,
      ),
    );
  }

  Widget _buildSkeletonLoader() {
    return ListView.builder(
      itemCount: 10,
      itemBuilder: (context, index) {
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const LoadingIndicator(
                  message: 'جاري التحميل...',
                  size: 16,
                ),
                const SizedBox(height: 8),
                Container(
                  width: 200,
                  height: 16,
                  color: Colors.grey[300],
                ),
                const SizedBox(height: 8),
                Container(
                  width: 150,
                  height: 16,
                  color: Colors.grey[300],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  SliverAppBar _buildSliverAppBar(
      bool isDarkMode, int notificationCount, bool isGoldTheme) {
    return SliverAppBar(
      expandedHeight: 140,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isGoldTheme
                  ? [Colors.black, Colors.grey[900]!]
                  : isDarkMode
                  ? [Colors.blue[900]!, Colors.blue[700]!]
                  : [Colors.blue[300]!, Colors.blue[500]!],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        _getTranslation('app_title', 'اكتشف مهنتك المستقبلية'),
                        style: GoogleFonts.tajawal(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: isGoldTheme ? Colors.amber[700] : Colors.white,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: _getTranslation(
                          'search_hint', 'ابحث عن مهنة أو دورة...'),
                      hintStyle: GoogleFonts.tajawal(
                          color:
                          isGoldTheme ? Colors.amber[100] : Colors.white70),
                      filled: true,
                      fillColor: isGoldTheme
                          ? Colors.grey[800]!.withOpacity(0.3)
                          : Colors.white.withOpacity(0.2),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide: BorderSide.none,
                      ),
                      prefixIcon: Icon(
                        Icons.search,
                        color: isGoldTheme ? Colors.amber[700] : Colors.white,
                      ),
                      suffixIcon: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (_searchController.text.isNotEmpty)
                            IconButton(
                              icon: Icon(
                                Icons.clear,
                                color: isGoldTheme
                                    ? Colors.amber[700]
                                    : Colors.white,
                              ),
                              onPressed: () {
                                _searchController.clear();
                                setState(() {});
                              },
                            ),
                          IconButton(
                            icon: Icon(
                              _speechToText.isListening
                                  ? Icons.mic
                                  : Icons.mic_none,
                              color: isGoldTheme
                                  ? Colors.amber[700]
                                  : Colors.white,
                            ),
                            onPressed: _speechToText.isListening
                                ? _stopListening
                                : _startListening,
                          ),
                        ],
                      ),
                    ),
                    style: GoogleFonts.tajawal(
                        color: isGoldTheme ? Colors.amber[100] : Colors.white),
                    onChanged: (value) {
                      if (value.isNotEmpty) {
                        _showSearchSuggestions(context, value, isGoldTheme);
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      actions: [
        Stack(
          children: [
            Consumer<AppState>(
              builder: (context, appState, child) {
                if (RoleChecker.isHokama(appState)) {
                  return IconButton(
                    icon: Icon(
                      Icons.add_circle_outline,
                      color: isGoldTheme ? Colors.amber[700] : Colors.blue,
                    ),
                    onPressed: () => _showOptionsDialog(context, isGoldTheme),
                    tooltip: 'إضافة محتوى جديد',
                  );
                }
                return const SizedBox.shrink();
              },
            ),
            if (notificationCount > 0)
              Positioned(
                right: 8,
                top: 8,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: isGoldTheme ? Colors.amber[700] : Colors.red,
                    shape: BoxShape.circle,
                  ),
                  constraints:
                  const BoxConstraints(minWidth: 16, minHeight: 16),
                  child: Text(
                    '$notificationCount',
                    style: TextStyle(
                      color: isGoldTheme ? Colors.black : Colors.white,
                      fontSize: 10,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
        IconButton(
          icon: Icon(
            Icons.person,
            color: isGoldTheme ? Colors.amber[700] : Colors.white,
          ),
          onPressed: () => _showProfileDialog(context, isGoldTheme),
        ),
      ],
    );
  }

  Widget _buildCategoryTabs(bool isGoldTheme) {
    return Container();
  }

  void _showSearchSuggestions(
      BuildContext context, String query, bool isGoldTheme) {
    // Placeholder implementation
  }

  Widget _buildCategorySection(BuildContext context, String categoryName,
      List<Profession> items, String lottieAsset, bool isGoldTheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            categoryName,
            style: GoogleFonts.cairo(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: isGoldTheme ? Colors.amber : Colors.black87,
            ),
          ),
        ),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: items.length,
            itemBuilder: (context, index) {
              final profession = items[index];
              return ProfessionCard(
                profession: profession,
                professionService: _professionService,
                onShare: () {
                  Share.share('تعرف على مهنة ${profession.title}!');
                  final appState =
                  Provider.of<AppState>(context, listen: false);
                  appState.addUserPoints(appState.userId ?? '', 5);
                },
                isGoldTheme: isGoldTheme,
                onContribute: () =>
                    _showContributionDialog(context, profession, isGoldTheme),
                onViewResources: () =>
                    _showResourcesDialog(context, profession, isGoldTheme),
                onViewCommunity: () =>
                    _showCommunityDialog(context, profession, isGoldTheme),
                onViewSubtopics: () =>
                    _showSubtopicsDialog(context, profession, isGoldTheme),
                onViewFAQ: () => _showFAQDialog(context, profession, isGoldTheme),
                onViewEvents: () =>
                    _showEventsDialog(context, profession, isGoldTheme),
                onViewMentors: () =>
                    _showMentorsDialog(context, profession, isGoldTheme),
                onViewStudyGroups: () =>
                    _showStudyGroupsDialog(context, profession, isGoldTheme),
              );
            },
          ),
        ),
      ],
    );
  }

  void _showContributionDialog(
      BuildContext context, Profession profession, bool isGoldTheme) {
    final resources = _professionsList.firstWhere(
            (p) => p['name'] == profession.title)['resources'] as List<String>;
    String? selectedResource;
    String? selectedSubtopic;
    final TextEditingController resourceController = TextEditingController();
    final TextEditingController previewController = TextEditingController();
    XFile? selectedMedia;
    bool isValidating = false;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: isGoldTheme ? Colors.grey[900] : Colors.white,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.4,
        maxChildSize: 0.9,
        builder: (context, scrollController) => StatefulBuilder(
          builder: (context, setDialogState) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getTranslation(
                      'contribute_resource', 'ساهم بمورد ل${profession.title}'),
                  style: GoogleFonts.tajawal(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isGoldTheme ? Colors.amber[700] : Colors.blue[700],
                  ),
                ),
                const SizedBox(height: 16),
                DropdownButton<String>(
                  value: selectedSubtopic,
                  hint: Text(
                    _getTranslation('select_subtopic', 'اختر موضوع فرعي'),
                    style: GoogleFonts.tajawal(
                        color: isGoldTheme ? Colors.white : Colors.black87),
                  ),
                  isExpanded: true,
                  items: (_subtopics[profession.title] ?? [])
                      .map((subtopic) => DropdownMenuItem(
                    value: subtopic.name,
                    child:
                    Text(subtopic.name, style: GoogleFonts.tajawal()),
                  ))
                      .toList(),
                  onChanged: (String? value) {
                    setDialogState(() {
                      selectedSubtopic = value;
                    });
                  },
                ),
                const SizedBox(height: 16),
                DropdownButton<String>(
                  value: selectedResource,
                  hint: Text(
                    _getTranslation('select_resource_type', 'اختر نوع المورد'),
                    style: GoogleFonts.tajawal(
                        color: isGoldTheme ? Colors.white : Colors.black87),
                  ),
                  isExpanded: true,
                  items: resources
                      .map((resource) => DropdownMenuItem(
                    value: resource,
                    child: Text(resource, style: GoogleFonts.tajawal()),
                  ))
                      .toList(),
                  onChanged: (String? value) {
                    setDialogState(() {
                      selectedResource = value;
                    });
                  },
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: resourceController,
                  decoration: InputDecoration(
                    labelText:
                    _getTranslation('resource_link', 'رابط أو وصف المورد'),
                    labelStyle: GoogleFonts.tajawal(
                        color: isGoldTheme ? Colors.white : Colors.black87),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: previewController,
                  decoration: InputDecoration(
                    labelText: _getTranslation(
                        'preview_url', 'رابط المعاينة (اختياري)'),
                    labelStyle: GoogleFonts.tajawal(
                        color: isGoldTheme ? Colors.white : Colors.black87),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () async {
                    final picker = ImagePicker();
                    final pickedFile = await picker.pickImage(
                        source: ImageSource.gallery);
                    if (pickedFile != null) {
                      setDialogState(() => selectedMedia = pickedFile);
                    }
                  },
                  child: Text(
                    selectedMedia == null ? 'اختر صورة/فيديو' : 'تم اختيار ملف',
                    style: GoogleFonts.tajawal(),
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: selectedResource != null &&
                            resourceController.text.isNotEmpty
                            ? () async {
                          setDialogState(() => isValidating = true);
                          final isValid = await _validateLink(
                              resourceController.text);
                          setDialogState(() => isValidating = false);
                          if (!isValid) {
                            _showSnackBar(
                                _getTranslation(
                                    'invalid_link', 'رابط غير صالح'),
                                Colors.red);
                            return;
                          }
                          String? mediaUrl;
                          if (selectedMedia != null) {
                            mediaUrl = await _uploadMedia(selectedMedia!);
                            if (mediaUrl == null) return;
                          }
                          final appState = Provider.of<AppState>(context,
                              listen: false);
                          final contribution = Contribution(
                            id: Uuid().v4(),
                            userId: appState.userId ?? '',
                            resource: resourceController.text,
                            resourceType: selectedResource!,
                            timestamp: DateTime.now(),
                            isApproved: false,
                            downloadCount: 0,
                            rating: 0.0,
                            comments: [],
                            previewUrl: previewController.text.isNotEmpty
                                ? previewController.text
                                : null,
                            mediaUrl: mediaUrl,
                            likes: 0,
                            subtopic: selectedSubtopic,
                          );
                          setState(() {
                            _contributions[profession.title] ??= [];
                            _contributions[profession.title]!
                                .add(contribution);
                          });
                          appState.socket?.emit('new_contribution', {
                            'id': contribution.id,
                            'userId': contribution.userId,
                            'profession': profession.title,
                            'resource': contribution.resource,
                            'resourceType': contribution.resourceType,
                            'previewUrl': contribution.previewUrl,
                            'mediaUrl': contribution.mediaUrl,
                            'subtopic': contribution.subtopic,
                          });
                          Navigator.pop(context);
                          _showSnackBar(
                            _getTranslation('contribution_success',
                                'شكرًا على مساهمتك! في انتظار الموافقة'),
                          );
                          await showDialog(
                            context: context,
                            builder: (context) => Dialog(
                              backgroundColor: Colors.transparent,
                              child: Lottie.asset(
                                'assets/animations/success.json',
                                width: 200,
                                height: 200,
                                repeat: false,
                              ),
                            ),
                          );
                        }
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                          isGoldTheme ? Colors.amber[700] : Colors.blue,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12)),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: isValidating
                            ? CircularProgressIndicator(
                            color:
                            isGoldTheme ? Colors.black : Colors.white)
                            : Text(
                          _getTranslation(
                              'submit_contribution', 'إرسال المساهمة'),
                          style: GoogleFonts.tajawal(
                            color:
                            isGoldTheme ? Colors.black : Colors.white,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showResourcesDialog(
      BuildContext context, Profession profession, bool isGoldTheme,
      {String? subtopicFilter}) {
    String selectedFilter = 'all';
    String? selectedSubtopic = subtopicFilter;
    final contributions =
        _contributions[profession.title]?.where((c) => c.isApproved).toList() ??
            [];

    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: isGoldTheme ? Colors.grey[900] : Colors.white,
        builder: (context) => DraggableScrollableSheet(
            initialChildSize: 0.7,
            builder: (context, scrollController) => StatefulBuilder(
            builder: (context, setDialogState) => Container(
    padding: const EdgeInsets.all(16),
    child: Column(
    children: [
    Text(
    _getTranslation('free_resources',
    'الموارد المجانية ل${profession.title}'),
    style: GoogleFonts.tajawal(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: isGoldTheme ? Colors.amber[700] : Colors.blue[700],
    ),
    ),
    DropdownButton<String>(
    value: selectedSubtopic,
    hint: Text(
    _getTranslation('select_subtopic', 'اختر موضوع فرعي'),
    style: GoogleFonts.tajawal(),
    ),
    isExpanded: true,
    items: (_subtopics[profession.title] ?? [])
        .map((subtopic) => DropdownMenuItem(
    value: subtopic.name,
    child:
    Text(subtopic.name                                , style: GoogleFonts.tajawal()),
    ))
        .toList()
      ..insert(
        0,
        DropdownMenuItem(
          value: null,
          child: Text(
            _getTranslation('all_subtopics', 'جميع المواضيع الفرعية'),
            style: GoogleFonts.tajawal(),
          ),
        ),
      ),
      onChanged: (String? value) {
        setDialogState(() {
          selectedSubtopic = value;
        });
      },
    ),
      const SizedBox(height: 16),
      DropdownButton<String>(
        value: selectedFilter,
        hint: Text(
          _getTranslation('filter_resources', 'تصفية الموارد'),
          style: GoogleFonts.tajawal(),
        ),
        isExpanded: true,
        items: [
          DropdownMenuItem(
            value: 'all',
            child: Text(
              _getTranslation('all', 'الكل'),
              style: GoogleFonts.tajawal(),
            ),
          ),
          DropdownMenuItem(
            value: 'most_downloaded',
            child: Text(
              _getTranslation('most_downloaded', 'الأكثر تنزيلًا'),
              style: GoogleFonts.tajawal(),
            ),
          ),
          DropdownMenuItem(
            value: 'highest_rated',
            child: Text(
              _getTranslation('highest_rated', 'الأعلى تقييمًا'),
              style: GoogleFonts.tajawal(),
            ),
          ),
        ],
        onChanged: (String? value) {
          setDialogState(() {
            selectedFilter = value ?? 'all';
          });
        },
      ),
      const SizedBox(height: 16),
      Expanded(
        child: contributions.isEmpty
            ? Center(
          child: Text(
            _getTranslation(
                'no_resources', 'لا توجد موارد متاحة بعد'),
            style: GoogleFonts.tajawal(fontSize: 16),
          ),
        )
            : ListView.builder(
          controller: scrollController,
          itemCount: contributions.length,
          itemBuilder: (context, index) {
            final contribution = contributions[index];
            if (selectedSubtopic != null &&
                contribution.subtopic != selectedSubtopic) {
              return const SizedBox.shrink();
            }
            return Card(
              color: isGoldTheme
                  ? Colors.grey[800]
                  : Colors.white,
              elevation: 2,
              margin: const EdgeInsets.symmetric(vertical: 8),
              child: ListTile(
                leading: contribution.mediaUrl != null
                    ? CachedNetworkImage(
                  imageUrl: contribution.mediaUrl!,
                  width: 50,
                  height: 50,
                  fit: BoxFit.cover,
                  placeholder: (context, url) =>
                  const CircularProgressIndicator(),
                  errorWidget: (context, url, error) =>
                  const Icon(Icons.error),
                )
                    : const Icon(Icons.description),
                title: Text(
                  contribution.resource,
                  style: GoogleFonts.tajawal(
                    fontWeight: FontWeight.bold,
                    color: isGoldTheme
                        ? Colors.amber[100]
                        : Colors.black87,
                  ),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${_getTranslation('type', 'النوع')}: ${contribution.resourceType}',
                      style: GoogleFonts.tajawal(
                          fontSize: 14,
                          color: isGoldTheme
                              ? Colors.white70
                              : Colors.grey[600]),
                    ),
                    Text(
                      '${_getTranslation('downloads', 'التنزيلات')}: ${contribution.downloadCount}',
                      style: GoogleFonts.tajawal(
                          fontSize: 14,
                          color: isGoldTheme
                              ? Colors.white70
                              : Colors.grey[600]),
                    ),
                    Row(
                      children: [
                        RatingBarIndicator(
                          rating: contribution.rating,
                          itemBuilder: (context, _) => Icon(
                            Icons.star,
                            color: isGoldTheme
                                ? Colors.amber
                                : Colors.blue,
                          ),
                          itemCount: 5,
                          itemSize: 20.0,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          contribution.rating.toStringAsFixed(1),
                          style: GoogleFonts.tajawal(
                              fontSize: 14,
                              color: isGoldTheme
                                  ? Colors.white70
                                  : Colors.grey[600]),
                        ),
                      ],
                    ),
                    if (contribution.subtopic != null)
                      Text(
                        '${_getTranslation('subtopic', 'الموضوع الفرعي')}: ${contribution.subtopic}',
                        style: GoogleFonts.tajawal(
                            fontSize: 14,
                            color: isGoldTheme
                                ? Colors.white70
                                : Colors.grey[600]),
                      ),
                  ],
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: Icon(
                        Icons.download,
                        color: isGoldTheme
                            ? Colors.amber[700]
                            : Colors.blue,
                      ),
                      onPressed: () {
                        final appState = Provider.of<AppState>(
                            context,
                            listen: false);
                        appState.socket?.emit(
                            'resource_downloaded', {
                          'id': contribution.id,
                          'profession': profession.title,
                          'userId': appState.userId,
                          'resource': contribution.resource,
                          'downloads':
                          contribution.downloadCount + 1,
                        });
                        _awardPoints(appState.userId ?? '', 2);
                      },
                    ),
                    IconButton(
                      icon: Icon(
                        contribution.likes > 0
                            ? Icons.favorite
                            : Icons.favorite_border,
                        color: contribution.likes > 0
                            ? Colors.red
                            : (isGoldTheme
                            ? Colors.amber[700]
                            : Colors.blue),
                      ),
                      onPressed: () {
                        setDialogState(() {
                          contribution.likes++;
                          appState.socket?.emit('resource_rated', {
                            'id': contribution.id,
                            'profession': profession.title,
                            'liked': true,
                          });
                        });
                        _awardPoints(
                            appState.userId ?? '', 1);
                      },
                    ),
                  ],
                ),
                onTap: contribution.previewUrl != null
                    ? () {
                  showDialog(
                    context: context,
                    builder: (context) => Dialog(
                      child: SizedBox(
                        height: 400,
                        child: contribution.previewUrl != null
                            ? WebViewWidget(
                                controller: WebViewController()
                                  ..setJavaScriptMode(JavaScriptMode.unrestricted)
                                  ..loadRequest(Uri.parse(contribution.previewUrl!))
                              )
                            : const Center(child: Text('No preview available')),
                      ),
                    ),
                  );
                }
                    : null,
              ),
            );
          },
        ),
      ),
    ],
    ),
            ),
            ),
        ),
    );
  }

  void _showCommunityDialog(
      BuildContext context, Profession profession, bool isGoldTheme) {
    final TextEditingController messageController = TextEditingController();
    String? selectedSubtopic;
    XFile? selectedMedia;
    bool isSending = false;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: isGoldTheme ? Colors.grey[900] : Colors.white,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        builder: (context, scrollController) => StatefulBuilder(
          builder: (context, setDialogState) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Text(
                  _getTranslation(
                      'community', 'منتدى ${profession.title}'),
                  style: GoogleFonts.tajawal(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isGoldTheme ? Colors.amber[700] : Colors.blue[700],
                  ),
                ),
                const SizedBox(height: 16),
                DropdownButton<String>(
                  value: selectedSubtopic,
                  hint: Text(
                    _getTranslation('select_subtopic', 'اختر موضوع فرعي'),
                    style: GoogleFonts.tajawal(),
                  ),
                  isExpanded: true,
                  items: (_subtopics[profession.title] ?? [])
                      .map((subtopic) => DropdownMenuItem(
                    value: subtopic.name,
                    child:
                    Text(subtopic.name, style: GoogleFonts.tajawal()),
                  ))
                      .toList(),
                  onChanged: (String? value) {
                    setDialogState(() {
                      selectedSubtopic = value;
                    });
                  },
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: messageController,
                  maxLines: 3,
                  decoration: InputDecoration(
                    labelText:
                    _getTranslation('your_message', 'رسالتك'),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () async {
                    final picker = ImagePicker();
                    final pickedFile = await picker.pickImage(
                        source: ImageSource.gallery);
                    if (pickedFile != null) {
                      setDialogState(() => selectedMedia = pickedFile);
                    }
                  },
                  child: Text(
                    selectedMedia == null ? 'اختر صورة' : 'تم اختيار صورة',
                    style: GoogleFonts.tajawal(),
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: isSending
                      ? null
                      : () async {
                    if (messageController.text.isEmpty) {
                      _showSnackBar(
                          _getTranslation(
                              'empty_message', 'الرجاء إدخال رسالة'),
                          Colors.red);
                      return;
                    }
                    setDialogState(() => isSending = true);
                    String? mediaUrl;
                    if (selectedMedia != null) {
                      mediaUrl = await _uploadMedia(selectedMedia!);
                      if (mediaUrl == null) {
                        setDialogState(() => isSending = false);
                        return;
                      }
                    }
                    final appState =
                    Provider.of<AppState>(context, listen: false);
                    final message = Message(
                      id: Uuid().v4(),
                      userId: appState.userId ?? '',
                      profession: profession.title,
                      subtopic: selectedSubtopic,
                      content: messageController.text,
                      timestamp: DateTime.now(),
                      mediaUrl: mediaUrl,
                      likes: 0,
                      comments: [],
                    );
                    setState(() {
                      _communityMessages.add(message);
                    });
                    appState.socket?.emit('new_message', {
                      'id': message.id,
                      'userId': message.userId,
                      'profession': message.profession,
                      'subtopic': message.subtopic,
                      'content': message.content,
                      'mediaUrl': message.mediaUrl,
                    });
                    setDialogState(() => isSending = false);
                    Navigator.pop(context);
                    _showSnackBar(
                      _getTranslation(
                          'message_posted', 'تم نشر رسالتك بنجاح'),
                      Colors.green,
                    );
                  },
                  child: isSending
                      ? CircularProgressIndicator(
                      color: isGoldTheme ? Colors.black : Colors.white)
                      : Text(
                    _getTranslation('post_message', 'نشر'),
                    style: GoogleFonts.tajawal(),
                  ),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: ListView.builder(
                    controller: scrollController,
                    itemCount: _communityMessages.length,
                    itemBuilder: (context, index) {
                      final message = _communityMessages[index];
                      if (message.profession != profession.title ||
                          (selectedSubtopic != null &&
                              message.subtopic != selectedSubtopic)) {
                        return const SizedBox.shrink();
                      }
                      return Card(
                        color: isGoldTheme ? Colors.grey[800] : Colors.white,
                        margin: const EdgeInsets.symmetric(vertical: 8),
                        child: ListTile(
                          leading: message.mediaUrl != null
                              ? CachedNetworkImage(
                            imageUrl: message.mediaUrl!,
                            width: 50,
                            height: 50,
                            fit: BoxFit.cover,
                            placeholder: (context, url) =>
                            const CircularProgressIndicator(),
                            errorWidget: (context, url, error) =>
                            const Icon(Icons.error),
                          )
                              : const Icon(Icons.message),
                          title: Text(
                            message.content,
                            style: GoogleFonts.tajawal(
                              fontWeight: FontWeight.bold,
                              color: isGoldTheme
                                  ? Colors.amber[100]
                                  : Colors.black87,
                            ),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                timeago.format(message.timestamp,
                                    locale: _currentLanguage),
                                style: GoogleFonts.tajawal(
                                  fontSize: 12,
                                  color: isGoldTheme
                                      ? Colors.white70
                                      : Colors.grey[600],
                                ),
                              ),
                              if (message.subtopic != null)
                                Text(
                                  '${_getTranslation('subtopic', 'الموضوع الفرعي')}: ${message.subtopic}',
                                  style: GoogleFonts.tajawal(
                                    fontSize: 12,
                                    color: isGoldTheme
                                        ? Colors.white70
                                        : Colors.grey[600],
                                  ),
                                ),
                            ],
                          ),
                          trailing: IconButton(
                            icon: Icon(
                              message.likes > 0
                                  ? Icons.favorite
                                  : Icons.favorite_border,
                              color: message.likes > 0
                                  ? Colors.red
                                  : (isGoldTheme
                                  ? Colors.amber[700]
                                  : Colors.blue),
                            ),
                            onPressed: () {
                              setState(() {
                                message.likes++;
                                appState.socket?.emit('message_liked', {
                                  'id': message.id,
                                  'profession': profession.title,
                                });
                              });
                              _awardPoints(appState.userId ?? '', 1);
                            },
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showSubtopicsDialog(
      BuildContext context, Profession profession, bool isGoldTheme) {
    final subtopics = _subtopics[profession.title] ?? [];

    showModalBottomSheet(
      context: context,
      backgroundColor: isGoldTheme ? Colors.grey[900] : Colors.white,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _getTranslation('subtopics', 'المواضيع الفرعية ل${profession.title}'),
              style: GoogleFonts.tajawal(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: isGoldTheme ? Colors.amber[700] : Colors.blue[700],
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: subtopics.isEmpty
                  ? Center(
                child: Text(
                  _getTranslation(
                      'no_subtopics', 'لا توجد مواضيع فرعية متاحة'),
                  style: GoogleFonts.tajawal(fontSize: 16),
                ),
              )
                  : ListView.builder(
                itemCount: subtopics.length,
                itemBuilder: (context, index) {
                  final subtopic = subtopics[index];
                  return ListTile(
                    title: Text(
                      subtopic.name,
                      style: GoogleFonts.tajawal(
                        color: isGoldTheme
                            ? Colors.amber[100]
                            : Colors.black87,
                      ),
                    ),
                    trailing: Icon(
                      Icons.arrow_forward,
                      color: isGoldTheme ? Colors.amber[700] : Colors.blue,
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      _showResourcesDialog(
                        context,
                        profession,
                        isGoldTheme,
                        subtopicFilter: subtopic.name,
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showFAQDialog(
      BuildContext context, Profession profession, bool isGoldTheme) {
    final faqs = _faqs[profession.title] ?? [];

    showModalBottomSheet(
      context: context,
      backgroundColor: isGoldTheme ? Colors.grey[900] : Colors.white,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _getTranslation('faq', 'الأسئلة الشائعة ل${profession.title}'),
              style: GoogleFonts.tajawal(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: isGoldTheme ? Colors.amber[700] : Colors.blue[700],
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: faqs.isEmpty
                  ? Center(
                child: Text(
                  _getTranslation('no_faqs', 'لا توجد أسئلة شائعة متاحة'),
                  style: GoogleFonts.tajawal(fontSize: 16),
                ),
              )
                  : ListView.builder(
                itemCount: faqs.length,
                itemBuilder: (context, index) {
                  final faq = faqs[index];
                  return ExpansionTile(
                    title: Text(
                      faq.question,
                      style: GoogleFonts.tajawal(
                        fontWeight: FontWeight.bold,
                        color: isGoldTheme
                            ? Colors.amber[100]
                            : Colors.black87,
                      ),
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Text(
                          faq.answer,
                          style: GoogleFonts.tajawal(
                            color: isGoldTheme
                                ? Colors.white70
                                : Colors.grey[600],
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showEventsDialog(
      BuildContext context, Profession profession, bool isGoldTheme) {
    final events = _events[profession.title] ?? [];

    showModalBottomSheet(
      context: context,
      backgroundColor: isGoldTheme ? Colors.grey[900] : Colors.white,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _getTranslation('events', 'الفعاليات وورش العمل ل${profession.title}'),
              style: GoogleFonts.tajawal(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: isGoldTheme ? Colors.amber[700] : Colors.blue[700],
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: events.isEmpty
                  ? Center(
                child: Text(
                  _getTranslation('no_events', 'لا توجد فعاليات متاحة'),
                  style: GoogleFonts.tajawal(fontSize: 16),
                ),
              )
                  : ListView.builder(
                itemCount: events.length,
                itemBuilder: (context, index) {
                  final event = events[index];
                  return Card(
                    color: isGoldTheme ? Colors.grey[800] : Colors.white,
                    margin: const EdgeInsets.symmetric(vertical: 8),
                    child: ListTile(
                      title: Text(
                        event.title,
                        style: GoogleFonts.tajawal(
                          fontWeight: FontWeight.bold,
                          color: isGoldTheme
                              ? Colors.amber[100]
                              : Colors.black87,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            DateFormat.yMMMd().format(event.date),
                            style: GoogleFonts.tajawal(
                              fontSize: 14,
                              color: isGoldTheme
                                  ? Colors.white70
                                  : Colors.grey[600],
                            ),
                          ),
                          Text(
                            event.isVirtual
                                ? _getTranslation(
                                'virtual_event', 'فعالية افتراضية')
                                : event.location,
                            style: GoogleFonts.tajawal(
                              fontSize: 14,
                              color: isGoldTheme
                                  ? Colors.white70
                                  : Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      trailing: Icon(
                        Icons.event,
                        color: isGoldTheme ? Colors.amber[700] : Colors.blue,
                      ),
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            title: Text(
                              event.title,
                              style: GoogleFonts.tajawal(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            content: Text(
                              event.description,
                              style: GoogleFonts.tajawal(),
                            ),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.pop(context),
                                child: Text(
                                  _getTranslation('close', 'إغلاق'),
                                  style: GoogleFonts.tajawal(),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showMentorsDialog(
      BuildContext context, Profession profession, bool isGoldTheme) {
    final mentors = _mentors[profession.title] ?? [];

    showModalBottomSheet(
      context: context,
      backgroundColor: isGoldTheme ? Colors.grey[900] : Colors.white,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _getTranslation('mentors', 'المرشدون ل${profession.title}'),
              style: GoogleFonts.tajawal(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: isGoldTheme ? Colors.amber[700] : Colors.blue[700],
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: mentors.isEmpty
                  ? Center(
                child: Text(
                  _getTranslation('no_mentors', 'لا توجد مرشدون متاحون'),
                  style: GoogleFonts.tajawal(fontSize: 16),
                ),
              )
                  : ListView.builder(
                itemCount: mentors.length,
                itemBuilder: (context, index) {
                  final mentor = mentors[index];
                  return Card(
                    color: isGoldTheme ? Colors.grey[800] : Colors.white,
                    margin: const EdgeInsets.symmetric(vertical: 8),
                    child: ListTile(
                      title: Text(
                        mentor.name,
                        style: GoogleFonts.tajawal(
                          fontWeight: FontWeight.bold,
                          color: isGoldTheme
                              ? Colors.amber[100]
                              : Colors.black87,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            mentor.expertise,
                            style: GoogleFonts.tajawal(
                              fontSize: 14,
                              color: isGoldTheme
                                  ? Colors.white70
                                  : Colors.grey[600],
                            ),
                          ),
                          Text(
                            mentor.contact,
                            style: GoogleFonts.tajawal(
                              fontSize: 14,
                              color: isGoldTheme
                                  ? Colors.white70
                                  : Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      trailing: Icon(
                        Icons.person,
                        color: isGoldTheme ? Colors.amber[700] : Colors.blue,
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showStudyGroupsDialog(
      BuildContext context, Profession profession, bool isGoldTheme) {
    final studyGroups = _studyGroups[profession.title] ?? [];

    showModalBottomSheet(
      context: context,
      backgroundColor: isGoldTheme ? Colors.grey[900] : Colors.white,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _getTranslation('study_groups', 'مجموعات دراسية ل${profession.title}'),
              style: GoogleFonts.tajawal(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: isGoldTheme ? Colors.amber[700] : Colors.blue[700],
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: studyGroups.isEmpty
                  ? Center(
                child: Text(
                  _getTranslation(
                      'no_study_groups', 'لا توجد مجموعات دراسية متاحة'),
                  style: GoogleFonts.tajawal(fontSize: 16),
                ),
              )
                  : ListView.builder(
                itemCount: studyGroups.length,
                itemBuilder: (context, index) {
                  final group = studyGroups[index];
                  return Card(
                    color: isGoldTheme ? Colors.grey[800] : Colors.white,
                    margin: const EdgeInsets.symmetric(vertical: 8),
                    child: ListTile(
                      title: Text(
                        group.name,
                        style: GoogleFonts.tajawal(
                          fontWeight: FontWeight.bold,
                          color: isGoldTheme
                              ? Colors.amber[100]
                              : Colors.black87,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            group.description,
                            style: GoogleFonts.tajawal(
                              fontSize: 14,
                              color: isGoldTheme
                                  ? Colors.white70
                                  : Colors.grey[600],
                            ),
                          ),
                          Text(
                            '${_getTranslation('members', 'الأعضاء')}: ${group.members.length}',
                            style: GoogleFonts.tajawal(
                              fontSize: 14,
                              color: isGoldTheme
                                  ? Colors.white70
                                  : Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      trailing: Icon(
                        Icons.group,
                        color: isGoldTheme ? Colors.amber[700] : Colors.blue,
                      ),
                      onTap: () {
                        final appState =
                        Provider.of<AppState>(context, listen: false);
                        if (!group.members.contains(appState.userId)) {
                          setState(() {
                            group.members.add(appState.userId ?? '');
                            appState.socket?.emit('join_study_group', {
                              'id': group.id,
                              'profession': profession.title,
                              'userId': appState.userId,
                            });
                          });
                          _showSnackBar(
                            _getTranslation(
                                'joined_group', 'تم الانضمام إلى المجموعة'),
                            Colors.green,
                          );
                        }
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showProfileDialog(BuildContext context, bool isGoldTheme) {
    final appState = Provider.of<AppState>(context, listen: false);
    final userPoints = appState.getUserPoints(appState.userId ?? '');
    final userBadges = appState.getUserBadges(appState.userId ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: isGoldTheme ? Colors.grey[900] : Colors.white,
        title: Text(
          _getTranslation('profile', 'ملفي'),
          style: GoogleFonts.tajawal(
            fontWeight: FontWeight.bold,
            color: isGoldTheme ? Colors.amber[700] : Colors.blue[700],
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${_getTranslation('points', 'النقاط')}: $userPoints',
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  color: isGoldTheme ? Colors.amber[100] : Colors.black87,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                _getTranslation('badges', 'الشارات'),
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isGoldTheme ? Colors.amber[100] : Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              userBadges.isEmpty
                  ? Text(
                _getTranslation('no_badges', 'لا توجد شارات بعد'),
                style: GoogleFonts.tajawal(
                  fontSize: 14,
                  color: isGoldTheme ? Colors.white70 : Colors.grey[600],
                ),
              )
                  : Wrap(
                spacing: 8,
                children: userBadges
                    .map((badge) => Chip(
                  label: Text(
                    badge,
                    style: GoogleFonts.tajawal(
                      color: isGoldTheme
                          ? Colors.black
                          : Colors.white,
                    ),
                  ),
                  backgroundColor: isGoldTheme
                      ? Colors.amber[700]
                      : Colors.blue,
                ))
                    .toList(),
              ),
              const SizedBox(height: 16),
              Text(
                _getTranslation('contributions', 'المساهمات'),
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isGoldTheme ? Colors.amber[100] : Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              ..._contributions.values
                  .expand((c) => c)
                  .where((c) => c.userId == appState.userId)
                  .map((c) => ListTile(
                title: Text(
                  c.resource,
                  style: GoogleFonts.tajawal(
                    color: isGoldTheme
                        ? Colors.amber[100]
                        : Colors.black87,
                  ),
                ),
                subtitle: Text(
                  c.isApproved
                      ? _getTranslation('approved', 'تمت الموافقة')
                      : _getTranslation('pending', 'في انتظار الموافقة'),
                  style: GoogleFonts.tajawal(
                    fontSize: 12,
                    color: isGoldTheme
                        ? Colors.white70
                        : Colors.grey[600],
                  ),
                ),
              ))
                  .toList(),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              _getTranslation('close', 'إغلاق'),
              style: GoogleFonts.tajawal(
                color: isGoldTheme ? Colors.amber[700] : Colors.blue,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ProfessionCard extends StatelessWidget {
  final Profession profession;
  final ProfessionService professionService;
  final VoidCallback onShare;
  final bool isGoldTheme;
  final VoidCallback onContribute;
  final VoidCallback onViewResources;
  final VoidCallback onViewCommunity;
  final VoidCallback onViewSubtopics;
  final VoidCallback onViewFAQ;
  final VoidCallback onViewEvents;
  final VoidCallback onViewMentors;
  final VoidCallback onViewStudyGroups;

  const ProfessionCard({
    super.key,
    required this.profession,
    required this.professionService,
    required this.onShare,
    required this.isGoldTheme,
    required this.onContribute,
    required this.onViewResources,
    required this.onViewCommunity,
    required this.onViewSubtopics,
    required this.onViewFAQ,
    required this.onViewEvents,
    required this.onViewMentors,
    required this.onViewStudyGroups,
  });

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    
    String _getTranslation(BuildContext context, String key, String defaultValue) {
      try {
        final appState = Provider.of<AppState>(context, listen: false);
        return appState.translations[appState.currentLanguage]?[key] ?? defaultValue;
      } catch (e) {
        return defaultValue;
      }
    }
    
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ProfessionDetailsPage(
              professionTitle: profession.title,
              profession: profession,
            ),
          ),
        );
      },
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Container(
          width: 200,
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CachedNetworkImage(
                imageUrl: profession.image,
                height: 80,
                width: double.infinity,
                fit: BoxFit.cover,
                placeholder: (context, url) => const CircularProgressIndicator(),
                errorWidget: (context, url, error) =>
                const Icon(Icons.broken_image, size: 80),
              ),
              const SizedBox(height: 8),
              Text(
                profession.title,
                style: GoogleFonts.tajawal(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isGoldTheme ? Colors.amber[100] : Colors.black87,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                '${profession.averageSalary} ${_getTranslation(context, 'currency', 'ريال')}',
                style: GoogleFonts.tajawal(
                  fontSize: 12,
                  color: isGoldTheme ? Colors.white70 : Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  IconButton(
                    icon: Icon(
                      Icons.share,
                      size: 20,
                      color: isGoldTheme ? Colors.amber[700] : Colors.blue,
                    ),
                    onPressed: onShare,
                  ),
                  PopupMenuButton<String>(
                    icon: Icon(
                      Icons.more_vert,
                      color: isGoldTheme ? Colors.amber[700] : Colors.blue,
                    ),
                    onSelected: (value) {
                      switch (value) {
                        case 'contribute':
                          onContribute();
                          break;
                        case 'resources':
                          onViewResources();
                          break;
                        case 'community':
                          onViewCommunity();
                          break;
                        case 'subtopics':
                          onViewSubtopics();
                          break;
                        case 'faq':
                          onViewFAQ();
                          break;
                        case 'events':
                          onViewEvents();
                          break;
                        case 'mentors':
                          onViewMentors();
                          break;
                        case 'study_groups':
                          onViewStudyGroups();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        value: 'contribute',
                        child: Text(
                          _getTranslation(context, 'contribute', 'ساهم'),
                          style: GoogleFonts.tajawal(),
                        ),
                      ),
                      PopupMenuItem(
                        value: 'resources',
                        child: Text(
                          _getTranslation(context, 'resources', 'الموارد'),
                          style: GoogleFonts.tajawal(),
                        ),
                      ),
                      PopupMenuItem(
                        value: 'community',
                        child: Text(
                          _getTranslation(context, 'community', 'المنتدى'),
                          style: GoogleFonts.tajawal(),
                        ),
                      ),
                      PopupMenuItem(
                        value: 'subtopics',
                        child: Text(
                          _getTranslation(context, 'subtopics', 'المواضيع الفرعية'),
                          style: GoogleFonts.tajawal(),
                        ),
                      ),
                      PopupMenuItem(
                        value: 'faq',
                        child: Text(
                          _getTranslation(context, 'faq', 'الأسئلة الشائعة'),
                          style: GoogleFonts.tajawal(),
                        ),
                      ),
                      PopupMenuItem(
                        value: 'events',
                        child: Text(
                          _getTranslation(context, 'events', 'الفعاليات ورش العمل'),
                          style: GoogleFonts.tajawal(),
                        ),
                      ),
                      PopupMenuItem(
                        value: 'mentors',
                        child: Text(
                          _getTranslation(context, 'mentors', 'المرشدون'),
                          style: GoogleFonts.tajawal(),
                        ),
                      ),
                      PopupMenuItem(
                        value: 'study_groups',
                        child: Text(
                          _getTranslation(context, 'study_groups', 'مجموعات دراسية'),
                          style: GoogleFonts.tajawal(),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }


}

class Contribution {
  final String id;
  final String userId;
  final String resource;
  final String resourceType;
  final DateTime timestamp;
  bool isApproved;
  int downloadCount;
  double rating;
  List<String> comments;
  final String? previewUrl;
  final String? mediaUrl;
  int likes;
  final String? subtopic;

  Contribution({
    required this.id,
    required this.userId,
    required this.resource,
    required this.resourceType,
    required this.timestamp,
    required this.isApproved,
    required this.downloadCount,
    required this.rating,
    required this.comments,
    this.previewUrl,
    this.mediaUrl,
    required this.likes,
    this.subtopic,
  });
}

class Subtopic {
  final String id;
  final String name;
  final String profession;

  Subtopic({
    required this.id,
    required this.name,
    required this.profession,
  });
}

class FAQ {
  final String id;
  final String question;
  final String answer;
  final String profession;

  FAQ({
    required this.id,
    required this.question,
    required this.answer,
    required this.profession,
  });
}

class Event {
  final String id;
  final String title;
  final String description;
  final DateTime date;
  final bool isVirtual;
  final String location;
  final String profession;

  Event({
    required this.id,
    required this.title,
    required this.description,
    required this.date,
    required this.isVirtual,
    required this.location,
    required this.profession,
  });
}

class Mentor {
  final String id;
  final String name;
  final String expertise;
  final String contact;
  final String profession;

  Mentor({
    required this.id,
    required this.name,
    required this.expertise,
    required this.contact,
    required this.profession,
  });
}

class StudyGroup {
  final String id;
  final String name;
  final String description;
  final List<String> members;
  final String profession;

  StudyGroup({
    required this.id,
    required this.name,
    required this.description,
    required this.members,
    required this.profession,
  });
}

class Message {
  final String id;
  final String userId;
  final String profession;
  final String? subtopic;
  final String content;
  final DateTime timestamp;
  final String? mediaUrl;
  int likes;
  final List<String> comments;

  Message({
    required this.id,
    required this.userId,
    required this.profession,
    this.subtopic,
    required this.content,
    required this.timestamp,
    this.mediaUrl,
    required this.likes,
    required this.comments,
  });
}
